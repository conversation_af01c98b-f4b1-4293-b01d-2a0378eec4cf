<Window x:Class="ArabicDashboard.InvoicesWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core"
        xmlns:dxg="http://schemas.devexpress.com/winfx/2008/xaml/grid"
        xmlns:dxe="http://schemas.devexpress.com/winfx/2008/xaml/editors"
        Title="🧾 إدارة الفواتير والسندات - مكتب الخدمات العامة"
        Height="900" Width="1600"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        dx:ThemeManager.ThemeName="Office2019Colorful"
        AllowsTransparency="False"
        WindowStyle="SingleBorderWindow">

    <Window.Background>
        <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#F8FAFC" Offset="0"/>
            <GradientStop Color="#E2E8F0" Offset="1"/>
        </LinearGradientBrush>
    </Window.Background>

    <Window.Effect>
        <DropShadowEffect Color="Black" Direction="315" ShadowDepth="10" Opacity="0.3" BlurRadius="20"/>
    </Window.Effect>

    <Window.Resources>
        <!-- أنماط الأزرار ثلاثية الأبعاد -->
        <Style x:Key="ModernButton3D" TargetType="Button">
            <Setter Property="Background">
                <Setter.Value>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                        <GradientStop Color="#4F46E5" Offset="0"/>
                        <GradientStop Color="#3730A3" Offset="1"/>
                    </LinearGradientBrush>
                </Setter.Value>
            </Setter>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,12"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Black" Direction="270" ShadowDepth="3" Opacity="0.3" BlurRadius="8"/>
                </Setter.Value>
            </Setter>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border"
                                Background="{TemplateBinding Background}"
                                CornerRadius="12"
                                Padding="{TemplateBinding Padding}">
                            <Border.Effect>
                                <DropShadowEffect x:Name="shadowEffect" Color="Black" Direction="270" ShadowDepth="3" Opacity="0.3" BlurRadius="8"/>
                            </Border.Effect>
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="shadowEffect" Property="ShadowDepth" Value="5"/>
                                <Setter TargetName="shadowEffect" Property="BlurRadius" Value="12"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="shadowEffect" Property="ShadowDepth" Value="1"/>
                                <Setter TargetName="shadowEffect" Property="BlurRadius" Value="4"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SuccessButton3D" TargetType="Button" BasedOn="{StaticResource ModernButton3D}">
            <Setter Property="Background">
                <Setter.Value>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                        <GradientStop Color="#10B981" Offset="0"/>
                        <GradientStop Color="#059669" Offset="1"/>
                    </LinearGradientBrush>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="DangerButton3D" TargetType="Button" BasedOn="{StaticResource ModernButton3D}">
            <Setter Property="Background">
                <Setter.Value>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                        <GradientStop Color="#EF4444" Offset="0"/>
                        <GradientStop Color="#DC2626" Offset="1"/>
                    </LinearGradientBrush>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="WarningButton3D" TargetType="Button" BasedOn="{StaticResource ModernButton3D}">
            <Setter Property="Background">
                <Setter.Value>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                        <GradientStop Color="#F59E0B" Offset="0"/>
                        <GradientStop Color="#D97706" Offset="1"/>
                    </LinearGradientBrush>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- أنماط البطاقات ثلاثية الأبعاد -->
        <Style x:Key="StatsCard" TargetType="Border">
            <Setter Property="CornerRadius" Value="16"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Black" Direction="270" ShadowDepth="8" Opacity="0.15" BlurRadius="25"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- أنماط الانتقالات -->
        <Storyboard x:Key="FadeInAnimation">
            <DoubleAnimation Storyboard.TargetProperty="Opacity" From="0" To="1" Duration="0:0:0.6"/>
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)"
                           From="20" To="0" Duration="0:0:0.6">
                <DoubleAnimation.EasingFunction>
                    <CubicEase EasingMode="EaseOut"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
        </Storyboard>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان المحسن -->
        <Border Grid.Row="0" Padding="32,24" Margin="0,0,0,8">
            <Border.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                    <GradientStop Color="#667EEA" Offset="0"/>
                    <GradientStop Color="#764BA2" Offset="1"/>
                </LinearGradientBrush>
            </Border.Background>
            <Border.Effect>
                <DropShadowEffect Color="Black" Direction="270" ShadowDepth="8" Opacity="0.2" BlurRadius="20"/>
            </Border.Effect>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" VerticalAlignment="Center">
                    <TextBlock Text="🧾 إدارة الفواتير والسندات" FontSize="28" FontWeight="Bold" Foreground="White"
                               Margin="0,0,0,8">
                        <TextBlock.Effect>
                            <DropShadowEffect Color="Black" Direction="270" ShadowDepth="2" Opacity="0.5" BlurRadius="4"/>
                        </TextBlock.Effect>
                    </TextBlock>
                    <TextBlock Text="إدارة شاملة لجميع الفواتير والسندات المالية بطريقة احترافية ومتقدمة"
                               FontSize="16" Foreground="#E2E8F0" FontWeight="Medium"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                    <Button x:Name="BtnAddInvoice"
                            Content="➕ إضافة فاتورة جديدة"
                            Style="{StaticResource SuccessButton3D}"
                            Click="BtnAddInvoice_Click"/>
                    <Button x:Name="BtnRefresh"
                            Content="🔄 تحديث البيانات"
                            Style="{StaticResource ModernButton3D}"
                            Click="BtnRefresh_Click"/>
                    <Button x:Name="BtnSearch"
                            Content="🔍 بحث متقدم"
                            Style="{StaticResource ModernButton3D}"
                            Click="BtnSearch_Click">
                        <Button.Background>
                            <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                <GradientStop Color="#8B5CF6" Offset="0"/>
                                <GradientStop Color="#7C3AED" Offset="1"/>
                            </LinearGradientBrush>
                        </Button.Background>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!-- المحتوى الرئيسي المحسن -->
        <Border Grid.Row="1" Margin="16">
            <Border.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                    <GradientStop Color="White" Offset="0"/>
                    <GradientStop Color="#F8FAFC" Offset="1"/>
                </LinearGradientBrush>
            </Border.Background>
            <Border.Effect>
                <DropShadowEffect Color="Black" Direction="270" ShadowDepth="12" Opacity="0.15" BlurRadius="30"/>
            </Border.Effect>
            <Border.RenderTransform>
                <TranslateTransform/>
            </Border.RenderTransform>
            <Border.Triggers>
                <EventTrigger RoutedEvent="Loaded">
                    <BeginStoryboard Storyboard="{StaticResource FadeInAnimation}"/>
                </EventTrigger>
            </Border.Triggers>

            <Grid Margin="32">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- عنوان الإحصائيات -->
                <TextBlock Grid.Row="0" Text="📊 إحصائيات سريعة" FontSize="22" FontWeight="Bold"
                           Foreground="#1F2937" Margin="0,0,0,24"/>

                <!-- إحصائيات سريعة محسنة -->
                <Grid Grid.Row="1" Margin="0,0,0,32">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- إجمالي الفواتير -->
                    <Border Grid.Column="0" Style="{StaticResource StatsCard}">
                        <Border.Background>
                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                <GradientStop Color="#EEF2FF" Offset="0"/>
                                <GradientStop Color="#E0E7FF" Offset="1"/>
                            </LinearGradientBrush>
                        </Border.Background>
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                                <TextBlock Text="📊" FontSize="24" Margin="0,0,12,0"/>
                                <TextBlock Text="إجمالي الفواتير" FontWeight="Bold" FontSize="16"
                                           Foreground="#4338CA" VerticalAlignment="Center"/>
                            </StackPanel>
                            <TextBlock x:Name="TxtTotalInvoices" Text="0" FontSize="32" FontWeight="Bold"
                                       Foreground="#4338CA" HorizontalAlignment="Center"/>
                        </StackPanel>
                    </Border>

                    <!-- الفواتير المسددة -->
                    <Border Grid.Column="1" Style="{StaticResource StatsCard}">
                        <Border.Background>
                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                <GradientStop Color="#D1FAE5" Offset="0"/>
                                <GradientStop Color="#A7F3D0" Offset="1"/>
                            </LinearGradientBrush>
                        </Border.Background>
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                                <TextBlock Text="✅" FontSize="24" Margin="0,0,12,0"/>
                                <TextBlock Text="مُسددة" FontWeight="Bold" FontSize="16"
                                           Foreground="#059669" VerticalAlignment="Center"/>
                            </StackPanel>
                            <TextBlock x:Name="TxtPaidInvoices" Text="0" FontSize="32" FontWeight="Bold"
                                       Foreground="#059669" HorizontalAlignment="Center"/>
                        </StackPanel>
                    </Border>

                    <!-- الفواتير المعلقة -->
                    <Border Grid.Column="2" Style="{StaticResource StatsCard}">
                        <Border.Background>
                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                <GradientStop Color="#FEF3C7" Offset="0"/>
                                <GradientStop Color="#FDE68A" Offset="1"/>
                            </LinearGradientBrush>
                        </Border.Background>
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                                <TextBlock Text="⏳" FontSize="24" Margin="0,0,12,0"/>
                                <TextBlock Text="معلقة" FontWeight="Bold" FontSize="16"
                                           Foreground="#D97706" VerticalAlignment="Center"/>
                            </StackPanel>
                            <TextBlock x:Name="TxtPendingInvoices" Text="0" FontSize="32" FontWeight="Bold"
                                       Foreground="#D97706" HorizontalAlignment="Center"/>
                        </StackPanel>
                    </Border>

                    <!-- إجمالي المبالغ -->
                    <Border Grid.Column="3" Style="{StaticResource StatsCard}">
                        <Border.Background>
                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                <GradientStop Color="#FEE2E2" Offset="0"/>
                                <GradientStop Color="#FECACA" Offset="1"/>
                            </LinearGradientBrush>
                        </Border.Background>
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                                <TextBlock Text="💰" FontSize="24" Margin="0,0,12,0"/>
                                <TextBlock Text="إجمالي المبالغ" FontWeight="Bold" FontSize="16"
                                           Foreground="#DC2626" VerticalAlignment="Center"/>
                            </StackPanel>
                            <TextBlock x:Name="TxtTotalAmount" Text="0 ريال" FontSize="28" FontWeight="Bold"
                                       Foreground="#DC2626" HorizontalAlignment="Center"/>
                        </StackPanel>
                    </Border>
                </Grid>

                <!-- جدول الفواتير المحسن -->
                <StackPanel Grid.Row="2">
                    <TextBlock Text="📋 قائمة الفواتير والسندات" FontSize="22" FontWeight="Bold"
                               Foreground="#1F2937" Margin="0,0,0,20"/>

                    <dxg:GridControl x:Name="GridInvoices" MinHeight="400" ShowBorder="False">
                        <dxg:GridControl.View>
                            <dxg:TableView ShowGroupPanel="False"
                                           AutoWidth="True"
                                           AllowEditing="False"
                                           ShowIndicator="True"
                                           NavigationStyle="Row"
                                           AlternatingRowBackground="#F8FAFC"
                                           FadeSelectionOnLostFocus="False"
                                           AllowColumnFiltering="True"
                                           AllowSorting="True"
                                           ShowAutoFilterRow="True"/>
                        </dxg:GridControl.View>

                        <dxg:GridControl.Columns>
                            <dxg:GridColumn FieldName="Id" Header="🔢 الرقم" Width="80" MinWidth="60">
                                <dxg:GridColumn.CellTemplate>
                                    <DataTemplate>
                                        <Border Background="#EEF2FF" CornerRadius="8" Padding="6,3" HorizontalAlignment="Center">
                                            <TextBlock Text="{Binding Id}" FontWeight="Bold" Foreground="#4338CA"
                                                       HorizontalAlignment="Center"/>
                                        </Border>
                                    </DataTemplate>
                                </dxg:GridColumn.CellTemplate>
                            </dxg:GridColumn>

                            <dxg:GridColumn FieldName="InvoiceNumber" Header="📄 رقم الفاتورة" Width="140" MinWidth="120">
                                <dxg:GridColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding InvoiceNumber}" FontWeight="SemiBold"
                                                   Foreground="#1F2937" VerticalAlignment="Center"/>
                                    </DataTemplate>
                                </dxg:GridColumn.CellTemplate>
                            </dxg:GridColumn>

                            <dxg:GridColumn FieldName="ClientName" Header="👤 اسم العميل" Width="180" MinWidth="150">
                                <dxg:GridColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                                            <Ellipse Width="8" Height="8" Fill="#10B981" Margin="0,0,8,0"/>
                                            <TextBlock Text="{Binding ClientName}" FontWeight="Medium"
                                                       VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </dxg:GridColumn.CellTemplate>
                            </dxg:GridColumn>

                            <dxg:GridColumn FieldName="PhoneNumber" Header="📱 رقم الهاتف" Width="140" MinWidth="120">
                                <dxg:GridColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding PhoneNumber}" FontFamily="Consolas"
                                                   Foreground="#6B7280" VerticalAlignment="Center"/>
                                    </DataTemplate>
                                </dxg:GridColumn.CellTemplate>
                            </dxg:GridColumn>

                            <dxg:GridColumn FieldName="TransactionType" Header="🏷️ نوع المعاملة" Width="200" MinWidth="180">
                                <dxg:GridColumn.CellTemplate>
                                    <DataTemplate>
                                        <Border Background="#F3F4F6" CornerRadius="12" Padding="8,4" HorizontalAlignment="Center">
                                            <TextBlock Text="{Binding TransactionType}" FontSize="12" FontWeight="Medium"
                                                       Foreground="#374151" HorizontalAlignment="Center"/>
                                        </Border>
                                    </DataTemplate>
                                </dxg:GridColumn.CellTemplate>
                            </dxg:GridColumn>

                            <dxg:GridColumn FieldName="Amount" Header="💰 المبلغ" Width="120" MinWidth="100">
                                <dxg:GridColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding Amount, StringFormat='{}{0:N0} ريال'}"
                                                   FontWeight="Bold" Foreground="#059669" VerticalAlignment="Center"
                                                   HorizontalAlignment="Center"/>
                                    </DataTemplate>
                                </dxg:GridColumn.CellTemplate>
                            </dxg:GridColumn>

                            <dxg:GridColumn FieldName="PaidAmount" Header="💳 المبلغ المدفوع" Width="140" MinWidth="120">
                                <dxg:GridColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding PaidAmount, StringFormat='{}{0:N0} ريال'}"
                                                   FontWeight="SemiBold" Foreground="#2563EB" VerticalAlignment="Center"
                                                   HorizontalAlignment="Center"/>
                                    </DataTemplate>
                                </dxg:GridColumn.CellTemplate>
                            </dxg:GridColumn>

                            <dxg:GridColumn FieldName="Status" Header="📊 الحالة" Width="120" MinWidth="100">
                                <dxg:GridColumn.CellTemplate>
                                    <DataTemplate>
                                        <Border CornerRadius="12" Padding="8,4" HorizontalAlignment="Center">
                                            <Border.Style>
                                                <Style TargetType="Border">
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding Status}" Value="مسددة">
                                                            <Setter Property="Background" Value="#D1FAE5"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Status}" Value="معلقة">
                                                            <Setter Property="Background" Value="#FEF3C7"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Status}" Value="ملغية">
                                                            <Setter Property="Background" Value="#FEE2E2"/>
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </Border.Style>
                                            <TextBlock Text="{Binding Status}" FontSize="12" FontWeight="Bold"
                                                       HorizontalAlignment="Center">
                                                <TextBlock.Style>
                                                    <Style TargetType="TextBlock">
                                                        <Style.Triggers>
                                                            <DataTrigger Binding="{Binding Status}" Value="مسددة">
                                                                <Setter Property="Foreground" Value="#059669"/>
                                                            </DataTrigger>
                                                            <DataTrigger Binding="{Binding Status}" Value="معلقة">
                                                                <Setter Property="Foreground" Value="#D97706"/>
                                                            </DataTrigger>
                                                            <DataTrigger Binding="{Binding Status}" Value="ملغية">
                                                                <Setter Property="Foreground" Value="#DC2626"/>
                                                            </DataTrigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </TextBlock.Style>
                                            </TextBlock>
                                        </Border>
                                    </DataTemplate>
                                </dxg:GridColumn.CellTemplate>
                            </dxg:GridColumn>

                            <dxg:GridColumn FieldName="CreatedDate" Header="📅 تاريخ الإنشاء" Width="140" MinWidth="120">
                                <dxg:GridColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding CreatedDate, StringFormat=dd/MM/yyyy}"
                                                   FontWeight="Medium" Foreground="#6B7280" VerticalAlignment="Center"/>
                                    </DataTemplate>
                                </dxg:GridColumn.CellTemplate>
                            </dxg:GridColumn>
                        </dxg:GridControl.Columns>

                        <dxg:GridControl.ContextMenu>
                            <ContextMenu>
                                <MenuItem Header="✏️ تعديل الفاتورة" Click="EditInvoice_Click">
                                    <MenuItem.Icon>
                                        <TextBlock Text="✏️" FontSize="14"/>
                                    </MenuItem.Icon>
                                </MenuItem>
                                <MenuItem Header="👁️ عرض التفاصيل" Click="ViewInvoice_Click">
                                    <MenuItem.Icon>
                                        <TextBlock Text="👁️" FontSize="14"/>
                                    </MenuItem.Icon>
                                </MenuItem>
                                <Separator/>
                                <MenuItem Header="🖨️ طباعة الفاتورة" Click="PrintInvoice_Click">
                                    <MenuItem.Icon>
                                        <TextBlock Text="🖨️" FontSize="14"/>
                                    </MenuItem.Icon>
                                </MenuItem>
                                <MenuItem Header="📄 تصدير PDF" Click="ExportInvoice_Click">
                                    <MenuItem.Icon>
                                        <TextBlock Text="📄" FontSize="14"/>
                                    </MenuItem.Icon>
                                </MenuItem>
                                <Separator/>
                                <MenuItem Header="💳 تسديد الفاتورة" Click="PayInvoice_Click">
                                    <MenuItem.Icon>
                                        <TextBlock Text="💳" FontSize="14"/>
                                    </MenuItem.Icon>
                                </MenuItem>
                                <MenuItem Header="🗑️ حذف الفاتورة" Click="DeleteInvoice_Click">
                                    <MenuItem.Icon>
                                        <TextBlock Text="🗑️" FontSize="14"/>
                                    </MenuItem.Icon>
                                </MenuItem>
                            </ContextMenu>
                        </dxg:GridControl.ContextMenu>
                    </dxg:GridControl>
                </StackPanel>
            </Grid>
        </Border>

        <!-- شريط الأدوات السفلي المحسن -->
        <Border Grid.Row="2" Padding="32,20" Margin="16,8,16,16">
            <Border.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                    <GradientStop Color="#F8FAFC" Offset="0"/>
                    <GradientStop Color="#E2E8F0" Offset="1"/>
                </LinearGradientBrush>
            </Border.Background>
            <Border.Effect>
                <DropShadowEffect Color="Black" Direction="90" ShadowDepth="4" Opacity="0.1" BlurRadius="12"/>
            </Border.Effect>

            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- إحصائيات سريعة -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <Border Background="#EEF2FF" CornerRadius="8" Padding="12,6" Margin="0,0,16,0">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="📊" FontSize="14" Margin="0,0,6,0"/>
                            <TextBlock Text="المحدد: " FontSize="12" Foreground="#4338CA"/>
                            <TextBlock x:Name="TxtSelectedCount" Text="0" FontSize="12" FontWeight="Bold" Foreground="#4338CA"/>
                        </StackPanel>
                    </Border>

                    <Border Background="#D1FAE5" CornerRadius="8" Padding="12,6">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="🕒" FontSize="14" Margin="0,0,6,0"/>
                            <TextBlock x:Name="TxtLastUpdate" Text="آخر تحديث: --" FontSize="12" Foreground="#059669"/>
                        </StackPanel>
                    </Border>
                </StackPanel>

                <!-- أزرار الإجراءات الرئيسية -->
                <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center">
                    <Button x:Name="BtnEdit"
                            Content="✏️ تعديل الفاتورة"
                            Style="{StaticResource ModernButton3D}"
                            Click="BtnEdit_Click"/>
                    <Button x:Name="BtnPrint"
                            Content="🖨️ طباعة"
                            Style="{StaticResource WarningButton3D}"
                            Click="BtnPrint_Click"/>
                    <Button x:Name="BtnExport"
                            Content="📄 تصدير PDF"
                            Style="{StaticResource ModernButton3D}"
                            Click="BtnExport_Click">
                        <Button.Background>
                            <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                <GradientStop Color="#8B5CF6" Offset="0"/>
                                <GradientStop Color="#7C3AED" Offset="1"/>
                            </LinearGradientBrush>
                        </Button.Background>
                    </Button>
                    <Button x:Name="BtnDelete"
                            Content="🗑️ حذف"
                            Style="{StaticResource DangerButton3D}"
                            Click="BtnDelete_Click"/>
                </StackPanel>

                <!-- أزرار إضافية -->
                <StackPanel Grid.Column="2" Orientation="Horizontal" HorizontalAlignment="Right" VerticalAlignment="Center">
                    <Button x:Name="BtnBulkActions"
                            Content="⚡ إجراءات مجمعة"
                            Style="{StaticResource ModernButton3D}"
                            Click="BtnBulkActions_Click">
                        <Button.Background>
                            <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                <GradientStop Color="#06B6D4" Offset="0"/>
                                <GradientStop Color="#0891B2" Offset="1"/>
                            </LinearGradientBrush>
                        </Button.Background>
                    </Button>
                    <Button x:Name="BtnReports"
                            Content="📈 التقارير"
                            Style="{StaticResource ModernButton3D}"
                            Click="BtnReports_Click">
                        <Button.Background>
                            <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                <GradientStop Color="#EC4899" Offset="0"/>
                                <GradientStop Color="#DB2777" Offset="1"/>
                            </LinearGradientBrush>
                        </Button.Background>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
