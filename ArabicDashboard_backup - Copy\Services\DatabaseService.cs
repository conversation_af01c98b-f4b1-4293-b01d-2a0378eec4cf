using ArabicDashboard.Data;
using ArabicDashboard.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.ObjectModel;
using System.IO;

namespace ArabicDashboard.Services
{
    public class DatabaseService
    {
        private readonly AppDbContext _context;

        public DatabaseService()
        {
            _context = new AppDbContext();
            InitializeDatabase();
        }

        private async void InitializeDatabase()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("بدء تهيئة قاعدة البيانات...");

                // التحقق من مسار قاعدة البيانات
                var dbPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "ArabicDashboard.db");
                System.Diagnostics.Debug.WriteLine($"مسار قاعدة البيانات: {dbPath}");

                // حذف قاعدة البيانات القديمة وإعادة إنشائها مع الحقول الجديدة
                if (File.Exists(dbPath))
                {
                    try
                    {
                        // محاولة التحقق من وجود جميع الجداول
                        var hasAllTables = _context.Database.CanConnect();
                        if (hasAllTables)
                        {
                            // التحقق من وجود جدول الإقامات
                            var residencesCount = _context.Residences.Count();
                            System.Diagnostics.Debug.WriteLine($"جدول الإقامات موجود - العدد: {residencesCount}");



                            // التحقق من وجود عمود IsEmployed في المعاملات العادية
                            var testQuery = _context.SimpleTransactions.Select(t => new { t.Id, t.IsEmployed }).FirstOrDefault();
                            System.Diagnostics.Debug.WriteLine("جميع الجداول والحقول موجودة");
                        }
                    }
                    catch
                    {
                        // إذا فشل الاستعلام، فهذا يعني أن الجداول أو الحقول غير موجودة
                        System.Diagnostics.Debug.WriteLine("جدول الإقامات أو الالتزامات الشخصية أو حقول التوظيف غير موجودة، سيتم إعادة إنشاء قاعدة البيانات...");
                        _context.Database.EnsureDeleted();
                        System.Diagnostics.Debug.WriteLine("تم حذف قاعدة البيانات القديمة");
                    }
                }

                // إنشاء قاعدة البيانات مع الحقول الجديدة
                var created = _context.Database.EnsureCreated();
                System.Diagnostics.Debug.WriteLine($"تم التأكد من وجود قاعدة البيانات - تم إنشاؤها: {created}");

                // التحقق من الجداول
                var canConnect = _context.Database.CanConnect();
                System.Diagnostics.Debug.WriteLine($"يمكن الاتصال بقاعدة البيانات: {canConnect}");

                // التحقق من وجود الجداول
                try
                {
                    var workersCount = _context.Workers.Count();
                    System.Diagnostics.Debug.WriteLine($"جدول العمال موجود - العدد الحالي: {workersCount}");

                    var transactionsCount = _context.SimpleTransactions.Count();
                    System.Diagnostics.Debug.WriteLine($"جدول المعاملات العادية موجود - العدد الحالي: {transactionsCount}");

                    var agentTransactionsCount = _context.AgentTransactions.Count();
                    System.Diagnostics.Debug.WriteLine($"جدول معاملات المنفذين موجود - العدد الحالي: {agentTransactionsCount}");

                    var residencesCount = _context.Residences.Count();
                    System.Diagnostics.Debug.WriteLine($"جدول الإقامات موجود - العدد الحالي: {residencesCount}");

                    var tasksCount = _context.Tasks.Count();
                    System.Diagnostics.Debug.WriteLine($"جدول المهام موجود - العدد الحالي: {tasksCount}");

                    // إضافة مهمة تجريبية إذا كان الجدول فارغ
                    if (tasksCount == 0)
                    {
                        var sampleTask = new TaskItem
                        {
                            Title = "مهمة تجريبية",
                            Description = "هذه مهمة تجريبية لاختبار النظام",
                            Priority = Models.TaskPriority.Medium,
                            Status = Models.TaskStatus.New,
                            CreatedDate = DateTime.Now,
                            DueDate = DateTime.Now.AddDays(7),
                            AssignedTo = "المدير",
                            CreatedBy = "النظام",
                            Notes = "مهمة تجريبية للاختبار"
                        };

                        _context.Tasks.Add(sampleTask);
                        await _context.SaveChangesAsync();
                        System.Diagnostics.Debug.WriteLine("تم إضافة مهمة تجريبية");
                    }
                }
                catch (Exception tableEx)
                {
                    System.Diagnostics.Debug.WriteLine($"خطأ في الوصول للجداول: {tableEx.Message}");

                    // إذا فشل الوصول للجداول، قم بإعادة إنشاء قاعدة البيانات
                    try
                    {
                        System.Diagnostics.Debug.WriteLine("محاولة إعادة إنشاء قاعدة البيانات...");
                        _context.Database.EnsureDeleted();
                        _context.Database.EnsureCreated();
                        System.Diagnostics.Debug.WriteLine("تم إعادة إنشاء قاعدة البيانات بنجاح");
                    }
                    catch (Exception recreateEx)
                    {
                        System.Diagnostics.Debug.WriteLine($"خطأ في إعادة إنشاء قاعدة البيانات: {recreateEx.Message}");
                    }
                }

                // إنشاء جدول الالتزامات إذا لم يكن موجوداً
                await CreateObligationsTableIfNotExistsAsync();

                System.Diagnostics.Debug.WriteLine("تم إنشاء قاعدة البيانات بنجاح مع جميع الحقول");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ مفصل في إنشاء قاعدة البيانات:");
                System.Diagnostics.Debug.WriteLine($"الرسالة: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"التفاصيل: {ex.InnerException?.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack Trace: {ex.StackTrace}");
            }
        }

        private async Task CreateObligationsTableIfNotExistsAsync()
        {
            try
            {
                var createTableSql = @"
                    CREATE TABLE IF NOT EXISTS Obligations (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Name TEXT NOT NULL,
                        Type TEXT NOT NULL,
                        Duration TEXT NOT NULL,
                        DueDate TEXT NOT NULL,
                        IsReminderEnabled INTEGER NOT NULL DEFAULT 1,
                        ReminderDaysBefore INTEGER NOT NULL DEFAULT 7,
                        Notes TEXT,
                        Amount DECIMAL(18,2) NOT NULL DEFAULT 0,
                        Status TEXT NOT NULL DEFAULT 'نشط',
                        CreatedDate TEXT NOT NULL,
                        UpdatedDate TEXT,
                        CreatedBy TEXT NOT NULL DEFAULT 'النظام'
                    )";

                await _context.Database.ExecuteSqlRawAsync(createTableSql);
                System.Diagnostics.Debug.WriteLine("تم إنشاء جدول الالتزامات بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء جدول الالتزامات: {ex.Message}");
            }
        }

        // ===== خدمات المعاملات العادية =====

        public async Task<List<SimpleTransaction>> GetAllSimpleTransactionsAsync()
        {
            try
            {
                var entities = await _context.SimpleTransactions.ToListAsync();
                return entities.Select(ConvertToSimpleTransaction).ToList();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في جلب المعاملات العادية: {ex.Message}");
                return new List<SimpleTransaction>();
            }
        }

        public async Task<bool> SaveSimpleTransactionAsync(SimpleTransaction transaction)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"بدء حفظ المعاملة العادية: {transaction.ClientName}");

                var entity = ConvertToSimpleTransactionEntity(transaction);

                System.Diagnostics.Debug.WriteLine($"تم تحويل المعاملة إلى Entity - ID: {entity.Id}");

                if (entity.Id == 0)
                {
                    // إضافة معاملة جديدة
                    _context.SimpleTransactions.Add(entity);
                    System.Diagnostics.Debug.WriteLine("تم إضافة المعاملة إلى Context");
                }
                else
                {
                    // تحديث معاملة موجودة
                    _context.SimpleTransactions.Update(entity);
                    System.Diagnostics.Debug.WriteLine($"تم تحديث المعاملة في Context - ID: {entity.Id}");
                }

                var result = await _context.SaveChangesAsync();
                System.Diagnostics.Debug.WriteLine($"نتيجة SaveChangesAsync: {result}");

                // تحديث ID في الكائن الأصلي
                transaction.Id = entity.Id;

                System.Diagnostics.Debug.WriteLine($"تم حفظ المعاملة العادية بنجاح - ID: {entity.Id}");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ مفصل في حفظ المعاملة العادية:");
                System.Diagnostics.Debug.WriteLine($"الرسالة: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"التفاصيل: {ex.InnerException?.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack Trace: {ex.StackTrace}");
                return false;
            }
        }

        public async Task<bool> AddSimpleTransactionAsync(SimpleTransaction transaction)
        {
            try
            {
                var entity = ConvertToSimpleTransactionEntity(transaction);
                _context.SimpleTransactions.Add(entity);
                var result = await _context.SaveChangesAsync();
                transaction.Id = entity.Id;
                return result > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إضافة المعاملة العادية: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> UpdateSimpleTransactionAsync(SimpleTransaction transaction)
        {
            try
            {
                // البحث عن المعاملة الموجودة أولاً
                var existingEntity = await _context.SimpleTransactions.FindAsync(transaction.Id);
                if (existingEntity == null)
                {
                    System.Diagnostics.Debug.WriteLine($"لم يتم العثور على المعاملة بالرقم: {transaction.Id}");
                    return false;
                }

                // تحديث القيم
                existingEntity.ClientName = transaction.ClientName;
                existingEntity.ServiceType = transaction.ServiceType;
                existingEntity.ServiceFees = transaction.ServiceFees;
                existingEntity.GovernmentFees = transaction.GovernmentFees;
                existingEntity.LaborOfficeFees = transaction.LaborOfficeFees;
                existingEntity.NetProfit = transaction.NetProfit;
                existingEntity.TotalAmount = transaction.TotalAmount;
                existingEntity.Status = transaction.Status;
                existingEntity.Notes = transaction.Notes;
                existingEntity.IsEmployed = transaction.IsEmployed;
                existingEntity.EmploymentAmount = transaction.EmploymentAmount;

                var result = await _context.SaveChangesAsync();
                System.Diagnostics.Debug.WriteLine($"تم تحديث المعاملة العادية بنجاح - ID: {transaction.Id}");
                return result > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث المعاملة العادية: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"تفاصيل الخطأ: {ex.StackTrace}");
                return false;
            }
        }

        public async Task<bool> DeleteSimpleTransactionAsync(int id)
        {
            try
            {
                var entity = await _context.SimpleTransactions.FindAsync(id);
                if (entity != null)
                {
                    _context.SimpleTransactions.Remove(entity);
                    await _context.SaveChangesAsync();
                    System.Diagnostics.Debug.WriteLine($"تم حذف المعاملة العادية - ID: {id}");
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حذف المعاملة العادية: {ex.Message}");
                return false;
            }
        }

        // ===== خدمات معاملات المنفذين =====

        public async Task<List<AgentTransaction>> GetAllAgentTransactionsAsync()
        {
            try
            {
                var entities = await _context.AgentTransactions.ToListAsync();
                return entities.Select(ConvertToAgentTransaction).ToList();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في جلب معاملات المنفذين: {ex.Message}");
                return new List<AgentTransaction>();
            }
        }

        public async Task<bool> SaveAgentTransactionAsync(AgentTransaction transaction)
        {
            try
            {
                var entity = ConvertToAgentTransactionEntity(transaction);
                
                if (entity.Id == 0)
                {
                    // إضافة معاملة جديدة
                    _context.AgentTransactions.Add(entity);
                }
                else
                {
                    // تحديث معاملة موجودة
                    _context.AgentTransactions.Update(entity);
                }

                await _context.SaveChangesAsync();
                
                // تحديث ID في الكائن الأصلي
                transaction.Id = entity.Id;
                
                System.Diagnostics.Debug.WriteLine($"تم حفظ معاملة المنفذ بنجاح - ID: {entity.Id}");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حفظ معاملة المنفذ: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> DeleteAgentTransactionAsync(int id)
        {
            try
            {
                var entity = await _context.AgentTransactions.FindAsync(id);
                if (entity != null)
                {
                    _context.AgentTransactions.Remove(entity);
                    await _context.SaveChangesAsync();
                    System.Diagnostics.Debug.WriteLine($"تم حذف معاملة المنفذ - ID: {id}");
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حذف معاملة المنفذ: {ex.Message}");
                return false;
            }
        }

        // ===== دوال التحويل =====

        private SimpleTransaction ConvertToSimpleTransaction(SimpleTransactionEntity entity)
        {
            return new SimpleTransaction
            {
                Id = entity.Id,
                ClientName = entity.ClientName,
                ServiceType = entity.ServiceType,
                ServiceFees = entity.ServiceFees,
                GovernmentFees = entity.GovernmentFees,
                LaborOfficeFees = entity.LaborOfficeFees,
                NetProfit = entity.NetProfit,
                TotalAmount = entity.TotalAmount,
                Status = entity.Status,
                CreatedDate = entity.CreatedDate,
                Notes = entity.Notes,
                IsEmployed = entity.IsEmployed,
                EmploymentAmount = entity.EmploymentAmount
            };
        }

        private SimpleTransactionEntity ConvertToSimpleTransactionEntity(SimpleTransaction transaction)
        {
            return new SimpleTransactionEntity
            {
                Id = transaction.Id,
                ClientName = transaction.ClientName,
                ServiceType = transaction.ServiceType,
                ServiceFees = transaction.ServiceFees,
                GovernmentFees = transaction.GovernmentFees,
                LaborOfficeFees = transaction.LaborOfficeFees,
                NetProfit = transaction.NetProfit,
                TotalAmount = transaction.TotalAmount,
                Status = transaction.Status,
                CreatedDate = transaction.CreatedDate,
                Notes = transaction.Notes,
                IsEmployed = transaction.IsEmployed,
                EmploymentAmount = transaction.EmploymentAmount
            };
        }

        private AgentTransaction ConvertToAgentTransaction(AgentTransactionEntity entity)
        {
            return new AgentTransaction
            {
                Id = entity.Id,
                AgentName = entity.AgentName,
                ServiceType = entity.ServiceType,
                ServiceFees = entity.ServiceFees,
                TotalAmount = entity.TotalAmount,
                NetProfit = entity.NetProfit,
                Status = entity.Status,
                TransferStatus = entity.TransferStatus,
                CreatedDate = entity.CreatedDate,
                Notes = entity.Notes
            };
        }

        private AgentTransactionEntity ConvertToAgentTransactionEntity(AgentTransaction transaction)
        {
            return new AgentTransactionEntity
            {
                Id = transaction.Id,
                AgentName = transaction.AgentName,
                ServiceType = transaction.ServiceType,
                ServiceFees = transaction.ServiceFees,
                TotalAmount = transaction.TotalAmount,
                NetProfit = transaction.NetProfit,
                Status = transaction.Status,
                TransferStatus = transaction.TransferStatus,
                CreatedDate = transaction.CreatedDate,
                Notes = transaction.Notes
            };
        }

        // ===== خدمات الإحصائيات =====

        public async Task<decimal> GetTotalMonthlyRevenueAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("بدء حساب الإيرادات الشهرية...");

                // حساب صافي الربح الشهري من المعاملات العادية
                var simpleTransactionsRevenue = await _context.SimpleTransactions
                    .Where(t => t.CreatedDate.Month == DateTime.Now.Month && t.CreatedDate.Year == DateTime.Now.Year)
                    .SumAsync(t => t.NetProfit); // تغيير من ServiceFees إلى NetProfit

                System.Diagnostics.Debug.WriteLine($"صافي الربح من المعاملات العادية: {simpleTransactionsRevenue}");

                // حساب صافي الربح الشهري من معاملات المنفذين
                var agentTransactionsRevenue = await _context.AgentTransactions
                    .Where(t => t.CreatedDate.Month == DateTime.Now.Month && t.CreatedDate.Year == DateTime.Now.Year)
                    .SumAsync(t => t.NetProfit);

                System.Diagnostics.Debug.WriteLine($"صافي الربح من معاملات المنفذين: {agentTransactionsRevenue}");

                var totalRevenue = simpleTransactionsRevenue + agentTransactionsRevenue;
                System.Diagnostics.Debug.WriteLine($"إجمالي صافي الربح الشهري: {totalRevenue}");

                return totalRevenue;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حساب الإيرادات الشهرية: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"تفاصيل الخطأ: {ex.StackTrace}");
                return 0;
            }
        }

        public async Task<int> GetTotalTransactionsCountAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("بدء حساب إجمالي عدد المعاملات...");

                // عدد المعاملات العادية
                var simpleTransactionsCount = await _context.SimpleTransactions.CountAsync();
                System.Diagnostics.Debug.WriteLine($"عدد المعاملات العادية: {simpleTransactionsCount}");

                // عدد معاملات المنفذين
                var agentTransactionsCount = await _context.AgentTransactions.CountAsync();
                System.Diagnostics.Debug.WriteLine($"عدد معاملات المنفذين: {agentTransactionsCount}");

                var totalCount = simpleTransactionsCount + agentTransactionsCount;
                System.Diagnostics.Debug.WriteLine($"إجمالي عدد المعاملات من قاعدة البيانات: {totalCount}");

                // طباعة تفاصيل المعاملات للتأكد
                var simpleTransactions = await _context.SimpleTransactions.ToListAsync();
                System.Diagnostics.Debug.WriteLine($"تفاصيل المعاملات العادية:");
                foreach (var transaction in simpleTransactions)
                {
                    System.Diagnostics.Debug.WriteLine($"  - ID: {transaction.Id}, العميل: {transaction.ClientName}, التاريخ: {transaction.CreatedDate}");
                }

                var agentTransactions = await _context.AgentTransactions.ToListAsync();
                System.Diagnostics.Debug.WriteLine($"تفاصيل معاملات المنفذين:");
                foreach (var transaction in agentTransactions)
                {
                    System.Diagnostics.Debug.WriteLine($"  - ID: {transaction.Id}, المنفذ: {transaction.AgentName}, التاريخ: {transaction.CreatedDate}");
                }

                return totalCount;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حساب عدد المعاملات: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"تفاصيل الخطأ: {ex.StackTrace}");
                return 0;
            }
        }

        public async Task<int> GetActiveWorkersCountAsync()
        {
            try
            {
                // حساب عدد العمال النشطين من جدول العمال
                var activeWorkersCount = await _context.Workers
                    .CountAsync(w => w.SubscriptionEndDate > DateTime.Now);

                System.Diagnostics.Debug.WriteLine($"عدد العمال النشطين من قاعدة البيانات: {activeWorkersCount}");
                return activeWorkersCount;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حساب عدد العمال النشطين: {ex.Message}");
                return 0;
            }
        }



        // ===== خدمات العمال =====

        public async Task<List<Worker>> GetAllWorkersAsync()
        {
            try
            {
                var workers = await _context.Workers.ToListAsync();
                System.Diagnostics.Debug.WriteLine($"تم جلب {workers.Count} عامل من قاعدة البيانات");
                return workers;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في جلب العمال: {ex.Message}");
                return new List<Worker>();
            }
        }

        public async Task<bool> SaveWorkerAsync(Worker worker)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"بدء حفظ العامل: {worker.WorkerName}");

                if (worker.Id == 0)
                {
                    // إضافة عامل جديد
                    _context.Workers.Add(worker);
                    System.Diagnostics.Debug.WriteLine("تم إضافة العامل إلى Context");
                }
                else
                {
                    // تحديث عامل موجود
                    _context.Workers.Update(worker);
                    System.Diagnostics.Debug.WriteLine($"تم تحديث العامل في Context - ID: {worker.Id}");
                }

                var result = await _context.SaveChangesAsync();
                System.Diagnostics.Debug.WriteLine($"نتيجة SaveChangesAsync: {result}");

                System.Diagnostics.Debug.WriteLine($"تم حفظ العامل بنجاح - ID: {worker.Id}");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ مفصل في حفظ العامل:");
                System.Diagnostics.Debug.WriteLine($"الرسالة: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"التفاصيل: {ex.InnerException?.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack Trace: {ex.StackTrace}");
                return false;
            }
        }

        public async Task<bool> DeleteWorkerAsync(int id)
        {
            try
            {
                var worker = await _context.Workers.FindAsync(id);
                if (worker != null)
                {
                    _context.Workers.Remove(worker);
                    await _context.SaveChangesAsync();
                    System.Diagnostics.Debug.WriteLine($"تم حذف العامل - ID: {id}");
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حذف العامل: {ex.Message}");
                return false;
            }
        }

        public async Task<int> GetActiveWorkersCountFromDatabaseAsync()
        {
            try
            {
                // حساب عدد العمال النشطين (الذين لم تنته اشتراكاتهم)
                var activeWorkersCount = await _context.Workers
                    .CountAsync(w => w.SubscriptionEndDate > DateTime.Now);

                System.Diagnostics.Debug.WriteLine($"عدد العمال النشطين من قاعدة البيانات: {activeWorkersCount}");
                return activeWorkersCount;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حساب عدد العمال النشطين: {ex.Message}");
                return 0;
            }
        }

        // ===== خدمات الإقامات =====

        public async Task<List<Residence>> GetAllResidencesAsync()
        {
            try
            {
                var residences = await _context.Residences.ToListAsync();
                System.Diagnostics.Debug.WriteLine($"تم جلب {residences.Count} إقامة من قاعدة البيانات");
                return residences;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في جلب الإقامات: {ex.Message}");
                return new List<Residence>();
            }
        }

        public async Task<bool> SaveResidenceAsync(Residence residence)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"بدء حفظ الإقامة: {residence.WorkerName}");
                System.Diagnostics.Debug.WriteLine($"تفاصيل الإقامة:");
                System.Diagnostics.Debug.WriteLine($"  - ID: {residence.Id}");
                System.Diagnostics.Debug.WriteLine($"  - اسم العامل: {residence.WorkerName}");
                System.Diagnostics.Debug.WriteLine($"  - رقم الإقامة: {residence.ResidenceNumber}");
                System.Diagnostics.Debug.WriteLine($"  - تاريخ التجديد: {residence.RenewalDate}");
                System.Diagnostics.Debug.WriteLine($"  - تاريخ انتهاء الإقامة: {residence.ExpiryDate}");
                System.Diagnostics.Debug.WriteLine($"  - تاريخ انتهاء التأمين: {residence.MedicalInsuranceExpiryDate}");
                System.Diagnostics.Debug.WriteLine($"  - الملاحظات: {residence.Notes}");

                // التحقق من حالة قاعدة البيانات
                var canConnect = _context.Database.CanConnect();
                System.Diagnostics.Debug.WriteLine($"يمكن الاتصال بقاعدة البيانات: {canConnect}");

                if (!canConnect)
                {
                    System.Diagnostics.Debug.WriteLine("لا يمكن الاتصال بقاعدة البيانات، محاولة إعادة الإنشاء...");
                    _context.Database.EnsureCreated();
                }

                // التحقق من وجود جدول الإقامات
                try
                {
                    var tableExists = await _context.Residences.AnyAsync();
                    System.Diagnostics.Debug.WriteLine($"جدول الإقامات موجود ويمكن الوصول إليه");
                }
                catch (Exception tableEx)
                {
                    System.Diagnostics.Debug.WriteLine($"خطأ في الوصول لجدول الإقامات: {tableEx.Message}");
                    System.Diagnostics.Debug.WriteLine("محاولة إعادة إنشاء قاعدة البيانات...");
                    _context.Database.EnsureDeleted();
                    _context.Database.EnsureCreated();
                }

                if (residence.Id == 0)
                {
                    // إضافة إقامة جديدة
                    _context.Residences.Add(residence);
                    System.Diagnostics.Debug.WriteLine("تم إضافة الإقامة إلى Context");
                }
                else
                {
                    // تحديث إقامة موجودة
                    _context.Residences.Update(residence);
                    System.Diagnostics.Debug.WriteLine($"تم تحديث الإقامة في Context - ID: {residence.Id}");
                }

                System.Diagnostics.Debug.WriteLine("محاولة حفظ التغييرات...");
                var result = await _context.SaveChangesAsync();
                System.Diagnostics.Debug.WriteLine($"نتيجة SaveChangesAsync: {result}");

                System.Diagnostics.Debug.WriteLine($"تم حفظ الإقامة بنجاح - ID: {residence.Id}");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ مفصل في حفظ الإقامة:");
                System.Diagnostics.Debug.WriteLine($"الرسالة: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"التفاصيل: {ex.InnerException?.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack Trace: {ex.StackTrace}");

                // محاولة إضافية لإعادة إنشاء قاعدة البيانات
                try
                {
                    System.Diagnostics.Debug.WriteLine("محاولة إعادة إنشاء قاعدة البيانات بسبب الخطأ...");
                    _context.Database.EnsureDeleted();
                    _context.Database.EnsureCreated();
                    System.Diagnostics.Debug.WriteLine("تم إعادة إنشاء قاعدة البيانات، محاولة الحفظ مرة أخرى...");

                    // محاولة الحفظ مرة أخرى
                    if (residence.Id == 0)
                    {
                        _context.Residences.Add(residence);
                    }
                    else
                    {
                        _context.Residences.Update(residence);
                    }

                    var retryResult = await _context.SaveChangesAsync();
                    System.Diagnostics.Debug.WriteLine($"نتيجة المحاولة الثانية: {retryResult}");
                    return true;
                }
                catch (Exception retryEx)
                {
                    System.Diagnostics.Debug.WriteLine($"فشلت المحاولة الثانية أيضاً: {retryEx.Message}");
                    return false;
                }
            }
        }

        public async Task<bool> DeleteResidenceAsync(int id)
        {
            try
            {
                var residence = await _context.Residences.FindAsync(id);
                if (residence != null)
                {
                    _context.Residences.Remove(residence);
                    await _context.SaveChangesAsync();
                    System.Diagnostics.Debug.WriteLine($"تم حذف الإقامة - ID: {id}");
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حذف الإقامة: {ex.Message}");
                return false;
            }
        }

        // إحصائيات الإقامات
        public async Task<int> GetTotalResidencesCountAsync()
        {
            try
            {
                var count = await _context.Residences.CountAsync();
                System.Diagnostics.Debug.WriteLine($"إجمالي عدد الإقامات: {count}");
                return count;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حساب عدد الإقامات: {ex.Message}");
                return 0;
            }
        }

        public async Task<int> GetExpiringResidencesCountAsync()
        {
            try
            {
                var count = await _context.Residences
                    .CountAsync(r => r.ExpiryDate <= DateTime.Now.AddDays(30) && r.ExpiryDate > DateTime.Now);
                System.Diagnostics.Debug.WriteLine($"عدد الإقامات التي ستنتهي خلال شهر: {count}");
                return count;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حساب الإقامات المنتهية قريباً: {ex.Message}");
                return 0;
            }
        }

        public async Task<int> GetExpiredResidencesCountAsync()
        {
            try
            {
                var count = await _context.Residences
                    .CountAsync(r => r.ExpiryDate <= DateTime.Now);
                System.Diagnostics.Debug.WriteLine($"عدد الإقامات المنتهية: {count}");
                return count;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حساب الإقامات المنتهية: {ex.Message}");
                return 0;
            }
        }

        public async Task<int> GetExpiringMedicalInsuranceCountAsync()
        {
            try
            {
                var count = await _context.Residences
                    .CountAsync(r => r.MedicalInsuranceExpiryDate <= DateTime.Now.AddDays(30) && r.MedicalInsuranceExpiryDate > DateTime.Now);
                System.Diagnostics.Debug.WriteLine($"عدد التأمينات الطبية التي ستنتهي خلال شهر: {count}");
                return count;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حساب التأمينات الطبية المنتهية قريباً: {ex.Message}");
                return 0;
            }
        }



        #region Shared Accounts Operations

        public async Task<List<SharedAccount>> GetAllSharedAccountsAsync()
        {
            try
            {
                var accounts = await _context.SharedAccounts
                    .OrderByDescending(a => a.TransactionDate)
                    .ToListAsync();

                System.Diagnostics.Debug.WriteLine($"تم جلب {accounts.Count} حساب مشترك من قاعدة البيانات");
                return accounts;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في جلب الحسابات المشتركة: {ex.Message}");
                return new List<SharedAccount>();
            }
        }

        public async Task<bool> SaveSharedAccountAsync(SharedAccount account)
        {
            try
            {
                if (account.Id == 0)
                {
                    // إضافة حساب جديد
                    _context.SharedAccounts.Add(account);
                    System.Diagnostics.Debug.WriteLine($"إضافة حساب مشترك جديد: {account.AccountName}");
                }
                else
                {
                    // تحديث حساب موجود
                    _context.SharedAccounts.Update(account);
                    System.Diagnostics.Debug.WriteLine($"تحديث الحساب المشترك: {account.AccountName} - ID: {account.Id}");
                }

                var result = await _context.SaveChangesAsync();
                System.Diagnostics.Debug.WriteLine($"تم حفظ الحساب المشترك بنجاح. عدد الصفوف المتأثرة: {result}");
                return result > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حفظ الحساب المشترك: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> DeleteSharedAccountAsync(int accountId)
        {
            try
            {
                var account = await _context.SharedAccounts.FindAsync(accountId);
                if (account != null)
                {
                    _context.SharedAccounts.Remove(account);
                    var result = await _context.SaveChangesAsync();
                    System.Diagnostics.Debug.WriteLine($"تم حذف الحساب المشترك: {account.AccountName} - ID: {accountId}");
                    return result > 0;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حذف الحساب المشترك: {ex.Message}");
                return false;
            }
        }

        public async Task<int> GetTotalSharedAccountsCountAsync()
        {
            try
            {
                var count = await _context.SharedAccounts.CountAsync();
                System.Diagnostics.Debug.WriteLine($"إجمالي عدد الحسابات المشتركة: {count}");
                return count;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حساب عدد الحسابات المشتركة: {ex.Message}");
                return 0;
            }
        }

        public async Task<decimal> GetTotalSharedAccountsAmountAsync()
        {
            try
            {
                var total = await _context.SharedAccounts
                    .SumAsync(a => a.TotalGrossAmount);
                System.Diagnostics.Debug.WriteLine($"إجمالي مبلغ الحسابات المشتركة: {total}");
                return total;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حساب إجمالي مبلغ الحسابات المشتركة: {ex.Message}");
                return 0;
            }
        }

        public async Task<decimal> GetMonthlySharedAccountsAmountAsync()
        {
            try
            {
                var startOfMonth = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
                var endOfMonth = startOfMonth.AddMonths(1).AddDays(-1);

                var total = await _context.SharedAccounts
                    .Where(a => a.TransactionDate >= startOfMonth && a.TransactionDate <= endOfMonth)
                    .SumAsync(a => a.TotalGrossAmount);

                System.Diagnostics.Debug.WriteLine($"إجمالي مبلغ الحسابات المشتركة هذا الشهر: {total}");
                return total;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حساب مبلغ الحسابات المشتركة الشهري: {ex.Message}");
                return 0;
            }
        }

        // دوال إحصائيات إضافية للحسابات المشتركة
        public async Task<decimal> GetTotalSharedAccountsProfitAsync()
        {
            try
            {
                var total = await _context.SharedAccounts
                    .SumAsync(a => a.NetProfit);
                System.Diagnostics.Debug.WriteLine($"إجمالي أرباح الحسابات المشتركة: {total}");
                return total;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حساب إجمالي أرباح الحسابات المشتركة: {ex.Message}");
                return 0;
            }
        }

        public async Task<decimal> GetMonthlySharedAccountsProfitAsync()
        {
            try
            {
                var startOfMonth = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
                var endOfMonth = startOfMonth.AddMonths(1).AddDays(-1);

                var total = await _context.SharedAccounts
                    .Where(a => a.TransactionDate >= startOfMonth && a.TransactionDate <= endOfMonth)
                    .SumAsync(a => a.NetProfit);

                System.Diagnostics.Debug.WriteLine($"إجمالي أرباح الحسابات المشتركة هذا الشهر: {total}");
                return total;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حساب أرباح الحسابات المشتركة الشهري: {ex.Message}");
                return 0;
            }
        }

        // دالة لإعادة حساب جميع الأرباح في قاعدة البيانات
        public async Task<bool> RecalculateAllProfitsAsync()
        {
            try
            {
                var accounts = await _context.SharedAccounts.ToListAsync();

                foreach (var account in accounts)
                {
                    // إعادة حساب صافي الربح وحصة الربح
                    account.NetProfit = account.TotalGrossAmount - account.TotalExpenses;
                    account.ProfitShare = account.NetProfit / 2;
                }

                var result = await _context.SaveChangesAsync();
                System.Diagnostics.Debug.WriteLine($"تم إعادة حساب الأرباح لـ {accounts.Count} حساب مشترك");
                return result > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إعادة حساب الأرباح: {ex.Message}");
                return false;
            }
        }

        #endregion

        // ===== خدمات الفواتير والسندات =====

        public async Task<bool> SaveInvoiceAsync(Invoice invoice)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"بدء حفظ الفاتورة: {invoice.InvoiceNumber}");

                // التأكد من وجود قاعدة البيانات
                try
                {
                    await _context.Database.EnsureCreatedAsync();
                    System.Diagnostics.Debug.WriteLine("تم التأكد من وجود قاعدة البيانات");
                }
                catch (Exception dbEx)
                {
                    System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء قاعدة البيانات: {dbEx.Message}");
                    // محاولة إعادة إنشاء قاعدة البيانات
                    await _context.Database.EnsureDeletedAsync();
                    await _context.Database.EnsureCreatedAsync();
                }

                if (invoice.Id == 0)
                {
                    // إضافة فاتورة جديدة
                    _context.Invoices.Add(invoice);
                    System.Diagnostics.Debug.WriteLine("تم إضافة الفاتورة إلى Context");
                }
                else
                {
                    // تحديث فاتورة موجودة
                    invoice.UpdatedDate = DateTime.Now;
                    _context.Invoices.Update(invoice);
                    System.Diagnostics.Debug.WriteLine($"تم تحديث الفاتورة في Context - ID: {invoice.Id}");
                }

                var result = await _context.SaveChangesAsync();
                System.Diagnostics.Debug.WriteLine($"نتيجة SaveChangesAsync: {result}");

                System.Diagnostics.Debug.WriteLine($"تم حفظ الفاتورة بنجاح - ID: {invoice.Id}");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ مفصل في حفظ الفاتورة:");
                System.Diagnostics.Debug.WriteLine($"الرسالة: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"التفاصيل: {ex.InnerException?.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack Trace: {ex.StackTrace}");

                // محاولة إضافية لإعادة إنشاء قاعدة البيانات
                try
                {
                    System.Diagnostics.Debug.WriteLine("محاولة إعادة إنشاء قاعدة البيانات بسبب الخطأ...");
                    await _context.Database.EnsureDeletedAsync();
                    await _context.Database.EnsureCreatedAsync();
                    System.Diagnostics.Debug.WriteLine("تم إعادة إنشاء قاعدة البيانات، محاولة الحفظ مرة أخرى...");

                    // محاولة الحفظ مرة أخرى
                    if (invoice.Id == 0)
                    {
                        _context.Invoices.Add(invoice);
                    }
                    else
                    {
                        _context.Invoices.Update(invoice);
                    }

                    var retryResult = await _context.SaveChangesAsync();
                    System.Diagnostics.Debug.WriteLine($"نتيجة المحاولة الثانية: {retryResult}");
                    return true;
                }
                catch (Exception retryEx)
                {
                    System.Diagnostics.Debug.WriteLine($"فشلت المحاولة الثانية أيضاً: {retryEx.Message}");
                    return false;
                }
            }
        }

        public async Task<bool> DeleteInvoiceAsync(int id)
        {
            try
            {
                var invoice = await _context.Invoices.FindAsync(id);
                if (invoice != null)
                {
                    _context.Invoices.Remove(invoice);
                    await _context.SaveChangesAsync();
                    System.Diagnostics.Debug.WriteLine($"تم حذف الفاتورة - ID: {id}");
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حذف الفاتورة: {ex.Message}");
                return false;
            }
        }

        // ===== وظائف الفواتير الإضافية =====

        public async Task<List<Invoice>> GetAllInvoicesAsync()
        {
            try
            {
                return await _context.Invoices
                    .Where(i => !i.IsDeleted)
                    .OrderByDescending(i => i.Id)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في جلب الفواتير: {ex.Message}");
                return new List<Invoice>();
            }
        }

        public async Task<Invoice?> GetInvoiceByIdAsync(int id)
        {
            try
            {
                return await _context.Invoices
                    .FirstOrDefaultAsync(i => i.Id == id && !i.IsDeleted);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في جلب الفاتورة: {ex.Message}");
                return null;
            }
        }

        public async Task<bool> AddInvoiceAsync(Invoice invoice)
        {
            try
            {
                invoice.CreatedDate = DateTime.Now;
                invoice.IsActive = true;
                invoice.IsDeleted = false;

                _context.Invoices.Add(invoice);
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إضافة الفاتورة: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> UpdateInvoiceAsync(Invoice invoice)
        {
            try
            {
                var existingInvoice = await _context.Invoices.FindAsync(invoice.Id);
                if (existingInvoice == null) return false;

                // تحديث الخصائص
                existingInvoice.ClientName = invoice.ClientName;
                existingInvoice.PhoneNumber = invoice.PhoneNumber;
                existingInvoice.ClientIdNumber = invoice.ClientIdNumber;
                existingInvoice.ClientAddress = invoice.ClientAddress;
                existingInvoice.TransactionType = invoice.TransactionType;
                existingInvoice.Amount = invoice.Amount;
                existingInvoice.PaidAmount = invoice.PaidAmount;
                existingInvoice.Status = invoice.Status;
                existingInvoice.IssueDate = invoice.IssueDate;
                existingInvoice.DueDate = invoice.DueDate;
                existingInvoice.Notes = invoice.Notes;
                existingInvoice.IsElectronic = invoice.IsElectronic;
                existingInvoice.UpdatedDate = DateTime.Now;
                existingInvoice.PrintedDate = invoice.PrintedDate;
                existingInvoice.PrintCount = invoice.PrintCount;
                existingInvoice.ExportedDate = invoice.ExportedDate;

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث الفاتورة: {ex.Message}");
                return false;
            }
        }

        public async Task<int> GetNextInvoiceNumberAsync(string prefix)
        {
            try
            {
                var lastInvoice = await _context.Invoices
                    .Where(i => i.InvoiceNumber.StartsWith(prefix))
                    .OrderByDescending(i => i.Id)
                    .FirstOrDefaultAsync();

                if (lastInvoice == null) return 1;

                // استخراج الرقم من رقم الفاتورة
                var numberPart = lastInvoice.InvoiceNumber.Substring(prefix.Length);
                if (int.TryParse(numberPart, out int lastNumber))
                {
                    return lastNumber + 1;
                }

                return 1;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في جلب رقم الفاتورة التالي: {ex.Message}");
                return DateTime.Now.Year * 1000 + DateTime.Now.DayOfYear;
            }
        }

        public async Task<string> GenerateInvoiceNumberAsync()
        {
            try
            {
                var today = DateTime.Now;
                var prefix = $"INV-{today:yyyyMM}-";

                var lastInvoice = await _context.Invoices
                    .Where(i => i.InvoiceNumber.StartsWith(prefix))
                    .OrderByDescending(i => i.Id)
                    .FirstOrDefaultAsync();

                int nextNumber = 1;
                if (lastInvoice != null)
                {
                    var lastNumberPart = lastInvoice.InvoiceNumber.Substring(prefix.Length);
                    if (int.TryParse(lastNumberPart, out int lastNumber))
                    {
                        nextNumber = lastNumber + 1;
                    }
                }

                return $"{prefix}{nextNumber:D4}";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في توليد رقم الفاتورة: {ex.Message}");
                return $"INV-{DateTime.Now:yyyyMM}-0001";
            }
        }



        // ===== وظائف الأرشفة =====

        public async Task<List<MoneyTransfer>> GetAllMoneyTransfersAsync()
        {
            try
            {
                var transfers = await _context.MoneyTransfers.ToListAsync();
                System.Diagnostics.Debug.WriteLine($"تم جلب {transfers.Count} حوالة مالية للأرشفة");
                return transfers;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في جلب الحوالات المالية للأرشفة: {ex.Message}");
                return new List<MoneyTransfer>();
            }
        }

        public async Task<int> GetMoneyTransfersCountAsync()
        {
            try
            {
                return await _context.MoneyTransfers.CountAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حساب عدد الحوالات المالية: {ex.Message}");
                return 0;
            }
        }

        public async Task<int> GetInvoicesCountAsync()
        {
            try
            {
                return await _context.Invoices.CountAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حساب عدد الفواتير: {ex.Message}");
                return 0;
            }
        }

        public async Task<int> GetSharedAccountsCountAsync()
        {
            try
            {
                return await _context.SharedAccounts.CountAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حساب عدد الحسابات المشتركة: {ex.Message}");
                return 0;
            }
        }



        // ===== وظائف تصفير البيانات للأرشفة =====

        public async Task ClearAllMoneyTransfersAsync()
        {
            try
            {
                var transfers = await _context.MoneyTransfers.ToListAsync();
                _context.MoneyTransfers.RemoveRange(transfers);
                await _context.SaveChangesAsync();
                System.Diagnostics.Debug.WriteLine($"تم تصفير {transfers.Count} حوالة مالية");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تصفير الحوالات المالية: {ex.Message}");
                throw;
            }
        }

        public async Task ClearAllInvoicesAsync()
        {
            try
            {
                var invoices = await _context.Invoices.ToListAsync();
                _context.Invoices.RemoveRange(invoices);
                await _context.SaveChangesAsync();
                System.Diagnostics.Debug.WriteLine($"تم تصفير {invoices.Count} فاتورة");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تصفير الفواتير: {ex.Message}");
                throw;
            }
        }

        public async Task ClearAllSharedAccountsAsync()
        {
            try
            {
                var accounts = await _context.SharedAccounts.ToListAsync();
                _context.SharedAccounts.RemoveRange(accounts);
                await _context.SaveChangesAsync();
                System.Diagnostics.Debug.WriteLine($"تم تصفير {accounts.Count} حساب مشترك");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تصفير الحسابات المشتركة: {ex.Message}");
                throw;
            }
        }

        public async Task ClearAllSimpleTransactionsAsync()
        {
            try
            {
                var transactions = await _context.SimpleTransactions.ToListAsync();
                _context.SimpleTransactions.RemoveRange(transactions);
                await _context.SaveChangesAsync();
                System.Diagnostics.Debug.WriteLine($"تم تصفير {transactions.Count} معاملة بسيطة");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تصفير المعاملات البسيطة: {ex.Message}");
                throw;
            }
        }

        public async Task ClearAllAgentTransactionsAsync()
        {
            try
            {
                var transactions = await _context.AgentTransactions.ToListAsync();
                _context.AgentTransactions.RemoveRange(transactions);
                await _context.SaveChangesAsync();
                System.Diagnostics.Debug.WriteLine($"تم تصفير {transactions.Count} معاملة منفذ");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تصفير معاملات المنفذين: {ex.Message}");
                throw;
            }
        }

        public async Task ResetStatisticsAsync()
        {
            try
            {
                // لا حاجة لتصفير الإحصائيات لأنها محسوبة ديناميكياً
                // هذه الوظيفة موجودة للتوافق مع خدمة الأرشفة
                System.Diagnostics.Debug.WriteLine("تم إعادة تعيين الإحصائيات (محسوبة ديناميكياً)");
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إعادة تعيين الإحصائيات: {ex.Message}");
                throw;
            }
        }

        // ===== وظائف الالتزامات =====

        public async Task<List<Obligation>> GetObligationsAsync()
        {
            try
            {
                var obligations = await _context.Obligations
                    .OrderByDescending(o => o.CreatedDate)
                    .ToListAsync();
                System.Diagnostics.Debug.WriteLine($"تم تحميل {obligations.Count} التزام");
                return obligations;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل الالتزامات: {ex.Message}");
                return new List<Obligation>();
            }
        }

        public async Task<bool> AddObligationAsync(Obligation obligation)
        {
            try
            {
                _context.Obligations.Add(obligation);
                await _context.SaveChangesAsync();
                System.Diagnostics.Debug.WriteLine($"تم إضافة التزام جديد: {obligation.Name}");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إضافة الالتزام: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> UpdateObligationAsync(Obligation obligation)
        {
            try
            {
                _context.Obligations.Update(obligation);
                await _context.SaveChangesAsync();
                System.Diagnostics.Debug.WriteLine($"تم تحديث الالتزام: {obligation.Name}");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث الالتزام: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> DeleteObligationAsync(int obligationId)
        {
            try
            {
                var obligation = await _context.Obligations.FindAsync(obligationId);
                if (obligation != null)
                {
                    _context.Obligations.Remove(obligation);
                    await _context.SaveChangesAsync();
                    System.Diagnostics.Debug.WriteLine($"تم حذف الالتزام: {obligation.Name}");
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حذف الالتزام: {ex.Message}");
                return false;
            }
        }

        public async Task<List<Obligation>> GetPendingObligationsAsync()
        {
            try
            {
                var pendingObligations = await _context.Obligations
                    .Where(o => o.Status == "نشط" && o.DueDate <= DateTime.Now.AddDays(7))
                    .OrderBy(o => o.DueDate)
                    .ToListAsync();
                System.Diagnostics.Debug.WriteLine($"تم تحميل {pendingObligations.Count} التزام مستحق");
                return pendingObligations;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل الالتزامات المستحقة: {ex.Message}");
                return new List<Obligation>();
            }
        }

        public async Task<int> GetObligationsCountAsync()
        {
            try
            {
                return await _context.Obligations.CountAsync(o => o.Status == "نشط");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حساب عدد الالتزامات: {ex.Message}");
                return 0;
            }
        }

        public async Task<int> GetPendingObligationsCountAsync()
        {
            try
            {
                return await _context.Obligations.CountAsync(o =>
                    o.Status == "نشط" &&
                    (o.DueDate <= DateTime.Now.AddDays(7) || o.DueDate < DateTime.Now));
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حساب عدد الالتزامات المستحقة: {ex.Message}");
                return 0;
            }
        }

        // ===== وظائف التذكيرات =====

        public async Task<bool> AddReminderAsync(Reminder reminder)
        {
            try
            {
                _context.Reminders.Add(reminder);
                await _context.SaveChangesAsync();
                System.Diagnostics.Debug.WriteLine($"تم إضافة تذكير جديد: {reminder.Title}");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إضافة التذكير: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> UpdateReminderAsync(Reminder reminder)
        {
            try
            {
                _context.Reminders.Update(reminder);
                await _context.SaveChangesAsync();
                System.Diagnostics.Debug.WriteLine($"تم تحديث التذكير: {reminder.Title}");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث التذكير: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> DeleteReminderAsync(int reminderId)
        {
            try
            {
                var reminder = await _context.Reminders.FindAsync(reminderId);
                if (reminder != null)
                {
                    _context.Reminders.Remove(reminder);
                    await _context.SaveChangesAsync();
                    System.Diagnostics.Debug.WriteLine($"تم حذف التذكير رقم: {reminderId}");
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حذف التذكير: {ex.Message}");
                return false;
            }
        }

        public async Task<List<Reminder>> GetAllRemindersAsync()
        {
            try
            {
                var reminders = await _context.Reminders
                    .OrderByDescending(r => r.ReminderDateTime)
                    .ToListAsync();
                System.Diagnostics.Debug.WriteLine($"تم جلب {reminders.Count} تذكير من قاعدة البيانات");
                return reminders;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في جلب التذكيرات: {ex.Message}");
                return new List<Reminder>();
            }
        }

        public async Task<Reminder?> GetReminderByIdAsync(int reminderId)
        {
            try
            {
                return await _context.Reminders.FindAsync(reminderId);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في جلب التذكير رقم {reminderId}: {ex.Message}");
                return null;
            }
        }

        public async Task<int> GetRemindersCountAsync()
        {
            try
            {
                return await _context.Reminders.CountAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حساب عدد التذكيرات: {ex.Message}");
                return 0;
            }
        }

        public async Task<int> GetActiveRemindersCountAsync()
        {
            try
            {
                return await _context.Reminders
                    .CountAsync(r => r.IsActive && !r.IsCompleted);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حساب عدد التذكيرات النشطة: {ex.Message}");
                return 0;
            }
        }

        public async Task<int> GetOverdueRemindersCountAsync()
        {
            try
            {
                var now = DateTime.Now;
                return await _context.Reminders
                    .CountAsync(r => r.IsActive && !r.IsCompleted && r.ReminderDateTime < now);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حساب عدد التذكيرات المتأخرة: {ex.Message}");
                return 0;
            }
        }

        // ===== خدمات المهام =====

        public async Task<List<TaskItem>> GetAllTasksAsync()
        {
            try
            {
                var tasks = await _context.Tasks.ToListAsync();
                System.Diagnostics.Debug.WriteLine($"تم جلب {tasks.Count} مهمة من قاعدة البيانات");
                return tasks;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في جلب المهام: {ex.Message}");
                return new List<TaskItem>();
            }
        }

        public async Task<bool> AddTaskAsync(TaskItem task)
        {
            try
            {
                _context.Tasks.Add(task);
                var result = await _context.SaveChangesAsync();
                System.Diagnostics.Debug.WriteLine($"تم إضافة المهمة بنجاح - ID: {task.Id}");
                return result > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إضافة المهمة: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> UpdateTaskAsync(TaskItem task)
        {
            try
            {
                var existingTask = await _context.Tasks.FindAsync(task.Id);
                if (existingTask == null)
                {
                    System.Diagnostics.Debug.WriteLine($"لم يتم العثور على المهمة بالرقم: {task.Id}");
                    return false;
                }

                // تحديث القيم
                existingTask.Title = task.Title;
                existingTask.Description = task.Description;
                existingTask.Priority = task.Priority;
                existingTask.Status = task.Status;
                existingTask.StartDate = task.StartDate;
                existingTask.DueDate = task.DueDate;
                existingTask.CompletedDate = task.CompletedDate;
                existingTask.AssignedTo = task.AssignedTo;
                existingTask.CreatedBy = task.CreatedBy;
                existingTask.Notes = task.Notes;
                existingTask.IsNotificationEnabled = task.IsNotificationEnabled;
                existingTask.LastNotificationDate = task.LastNotificationDate;
                existingTask.Progress = task.Progress;

                var result = await _context.SaveChangesAsync();
                System.Diagnostics.Debug.WriteLine($"تم تحديث المهمة بنجاح - ID: {task.Id}");
                return result > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث المهمة: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> SaveTaskAsync(TaskItem task)
        {
            try
            {
                if (task.Id == 0)
                {
                    // إضافة مهمة جديدة
                    return await AddTaskAsync(task);
                }
                else
                {
                    // تحديث مهمة موجودة
                    return await UpdateTaskAsync(task);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حفظ المهمة: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> DeleteTaskAsync(int id)
        {
            try
            {
                var task = await _context.Tasks.FindAsync(id);
                if (task != null)
                {
                    _context.Tasks.Remove(task);
                    var result = await _context.SaveChangesAsync();
                    System.Diagnostics.Debug.WriteLine($"تم حذف المهمة - ID: {id}");
                    return result > 0;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حذف المهمة: {ex.Message}");
                return false;
            }
        }

        public void Dispose()
        {
            _context?.Dispose();
        }
    }
}
