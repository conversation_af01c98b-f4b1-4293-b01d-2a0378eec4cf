﻿#pragma checksum "..\..\..\AddSharedAccountWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "1A6BF305ADF03FDD3369531DF07D3904BF7FF710"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using DevExpress.Core;
using DevExpress.Xpf.Core;
using DevExpress.Xpf.Core.ConditionalFormatting;
using DevExpress.Xpf.Core.DataSources;
using DevExpress.Xpf.Core.Serialization;
using DevExpress.Xpf.Core.ServerMode;
using DevExpress.Xpf.DXBinding;
using DevExpress.Xpf.Data;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace ArabicDashboard {
    
    
    /// <summary>
    /// AddSharedAccountWindow
    /// </summary>
    public partial class AddSharedAccountWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 22 "..\..\..\AddSharedAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTitle;
        
        #line default
        #line hidden
        
        
        #line 52 "..\..\..\AddSharedAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtAccountName;
        
        #line default
        #line hidden
        
        
        #line 64 "..\..\..\AddSharedAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtWorkerName;
        
        #line default
        #line hidden
        
        
        #line 76 "..\..\..\AddSharedAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtServiceType;
        
        #line default
        #line hidden
        
        
        #line 88 "..\..\..\AddSharedAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker DateTransaction;
        
        #line default
        #line hidden
        
        
        #line 108 "..\..\..\AddSharedAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtTotalAmount;
        
        #line default
        #line hidden
        
        
        #line 120 "..\..\..\AddSharedAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtSponsorAmount;
        
        #line default
        #line hidden
        
        
        #line 132 "..\..\..\AddSharedAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtGovernmentFees;
        
        #line default
        #line hidden
        
        
        #line 144 "..\..\..\AddSharedAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtOtherFees;
        
        #line default
        #line hidden
        
        
        #line 162 "..\..\..\AddSharedAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LblTotalExpenses;
        
        #line default
        #line hidden
        
        
        #line 172 "..\..\..\AddSharedAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LblNetProfit;
        
        #line default
        #line hidden
        
        
        #line 184 "..\..\..\AddSharedAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LblProfitShare;
        
        #line default
        #line hidden
        
        
        #line 200 "..\..\..\AddSharedAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Core.SimpleButton BtnSave;
        
        #line default
        #line hidden
        
        
        #line 211 "..\..\..\AddSharedAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Core.SimpleButton BtnCancel;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/ArabicDashboard;component/addsharedaccountwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\AddSharedAccountWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TxtTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.TxtAccountName = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.TxtWorkerName = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.TxtServiceType = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.DateTransaction = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 6:
            this.TxtTotalAmount = ((System.Windows.Controls.TextBox)(target));
            
            #line 109 "..\..\..\AddSharedAccountWindow.xaml"
            this.TxtTotalAmount.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.CalculateAmounts);
            
            #line default
            #line hidden
            return;
            case 7:
            this.TxtSponsorAmount = ((System.Windows.Controls.TextBox)(target));
            
            #line 121 "..\..\..\AddSharedAccountWindow.xaml"
            this.TxtSponsorAmount.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.CalculateAmounts);
            
            #line default
            #line hidden
            return;
            case 8:
            this.TxtGovernmentFees = ((System.Windows.Controls.TextBox)(target));
            
            #line 133 "..\..\..\AddSharedAccountWindow.xaml"
            this.TxtGovernmentFees.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.CalculateAmounts);
            
            #line default
            #line hidden
            return;
            case 9:
            this.TxtOtherFees = ((System.Windows.Controls.TextBox)(target));
            
            #line 145 "..\..\..\AddSharedAccountWindow.xaml"
            this.TxtOtherFees.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.CalculateAmounts);
            
            #line default
            #line hidden
            return;
            case 10:
            this.LblTotalExpenses = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.LblNetProfit = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.LblProfitShare = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.BtnSave = ((DevExpress.Xpf.Core.SimpleButton)(target));
            
            #line 209 "..\..\..\AddSharedAccountWindow.xaml"
            this.BtnSave.Click += new System.Windows.RoutedEventHandler(this.BtnSave_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.BtnCancel = ((DevExpress.Xpf.Core.SimpleButton)(target));
            
            #line 220 "..\..\..\AddSharedAccountWindow.xaml"
            this.BtnCancel.Click += new System.Windows.RoutedEventHandler(this.BtnCancel_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

