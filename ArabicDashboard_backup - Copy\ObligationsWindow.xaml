<Window x:Class="ArabicDashboard.ObligationsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core"
        xmlns:dxg="http://schemas.devexpress.com/winfx/2008/xaml/grid"
        xmlns:dxe="http://schemas.devexpress.com/winfx/2008/xaml/editors"
        xmlns:dxb="http://schemas.devexpress.com/winfx/2008/xaml/bars"
        xmlns:dxr="http://schemas.devexpress.com/winfx/2008/xaml/ribbon"
        Title="📋 إدارة الالتزامات - مكتب الخدمات العامة"
        Height="900" Width="1600"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        dx:ThemeManager.ThemeName="Office2019Colorful"
        AllowsTransparency="False"
        WindowStyle="SingleBorderWindow">

    <Window.Background>
        <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#F8FAFC" Offset="0"/>
            <GradientStop Color="#E2E8F0" Offset="1"/>
        </LinearGradientBrush>
    </Window.Background>

    <Window.Effect>
        <DropShadowEffect Color="Black" Direction="315" ShadowDepth="10" Opacity="0.3" BlurRadius="20"/>
    </Window.Effect>

    <Window.Resources>
        <!-- أنماط الأزرار ثلاثية الأبعاد -->
        <Style x:Key="ModernButton3D" TargetType="Button">
            <Setter Property="Background">
                <Setter.Value>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                        <GradientStop Color="#4F46E5" Offset="0"/>
                        <GradientStop Color="#3730A3" Offset="1"/>
                    </LinearGradientBrush>
                </Setter.Value>
            </Setter>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,12"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Black" Direction="270" ShadowDepth="3" Opacity="0.3" BlurRadius="8"/>
                </Setter.Value>
            </Setter>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border"
                                Background="{TemplateBinding Background}"
                                CornerRadius="12"
                                Padding="{TemplateBinding Padding}">
                            <Border.Effect>
                                <DropShadowEffect x:Name="shadowEffect" Color="Black" Direction="270" ShadowDepth="3" Opacity="0.3" BlurRadius="8"/>
                            </Border.Effect>
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background">
                                    <Setter.Value>
                                        <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                            <GradientStop Color="#5B21B6" Offset="0"/>
                                            <GradientStop Color="#4C1D95" Offset="1"/>
                                        </LinearGradientBrush>
                                    </Setter.Value>
                                </Setter>
                                <Setter TargetName="shadowEffect" Property="ShadowDepth" Value="5"/>
                                <Setter TargetName="shadowEffect" Property="BlurRadius" Value="12"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="border" Property="Background">
                                    <Setter.Value>
                                        <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                            <GradientStop Color="#3730A3" Offset="0"/>
                                            <GradientStop Color="#312E81" Offset="1"/>
                                        </LinearGradientBrush>
                                    </Setter.Value>
                                </Setter>
                                <Setter TargetName="shadowEffect" Property="ShadowDepth" Value="1"/>
                                <Setter TargetName="shadowEffect" Property="BlurRadius" Value="4"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SecondaryButton3D" TargetType="Button" BasedOn="{StaticResource ModernButton3D}">
            <Setter Property="Background">
                <Setter.Value>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                        <GradientStop Color="#6B7280" Offset="0"/>
                        <GradientStop Color="#4B5563" Offset="1"/>
                    </LinearGradientBrush>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SuccessButton3D" TargetType="Button" BasedOn="{StaticResource ModernButton3D}">
            <Setter Property="Background">
                <Setter.Value>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                        <GradientStop Color="#10B981" Offset="0"/>
                        <GradientStop Color="#059669" Offset="1"/>
                    </LinearGradientBrush>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="DangerButton3D" TargetType="Button" BasedOn="{StaticResource ModernButton3D}">
            <Setter Property="Background">
                <Setter.Value>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                        <GradientStop Color="#EF4444" Offset="0"/>
                        <GradientStop Color="#DC2626" Offset="1"/>
                    </LinearGradientBrush>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="WarningButton3D" TargetType="Button" BasedOn="{StaticResource ModernButton3D}">
            <Setter Property="Background">
                <Setter.Value>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                        <GradientStop Color="#F59E0B" Offset="0"/>
                        <GradientStop Color="#D97706" Offset="1"/>
                    </LinearGradientBrush>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- أنماط البطاقات ثلاثية الأبعاد -->
        <Style x:Key="Card3D" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="16"/>
            <Setter Property="Padding" Value="24"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Black" Direction="270" ShadowDepth="8" Opacity="0.15" BlurRadius="25"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- أنماط الانتقالات -->
        <Storyboard x:Key="FadeInAnimation">
            <DoubleAnimation Storyboard.TargetProperty="Opacity" From="0" To="1" Duration="0:0:0.5"/>
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)"
                           From="20" To="0" Duration="0:0:0.5">
                <DoubleAnimation.EasingFunction>
                    <CubicEase EasingMode="EaseOut"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
        </Storyboard>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- الشريط العلوي المحسن -->
        <Border Grid.Row="0" Style="{StaticResource Card3D}" Margin="0,0,0,8">
            <Border.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                    <GradientStop Color="#667EEA" Offset="0"/>
                    <GradientStop Color="#764BA2" Offset="1"/>
                </LinearGradientBrush>
            </Border.Background>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- العنوان والوصف -->
                <StackPanel Grid.Column="0" VerticalAlignment="Center">
                    <TextBlock Text="📋 إدارة الالتزامات" FontSize="28" FontWeight="Bold" Foreground="White"
                               Margin="0,0,0,8">
                        <TextBlock.Effect>
                            <DropShadowEffect Color="Black" Direction="270" ShadowDepth="2" Opacity="0.5" BlurRadius="4"/>
                        </TextBlock.Effect>
                    </TextBlock>
                    <TextBlock Text="إدارة وتتبع جميع الالتزامات والمواعيد المهمة بطريقة احترافية ومتقدمة"
                               FontSize="16" Foreground="#E2E8F0" FontWeight="Medium"/>
                </StackPanel>

                <!-- أزرار الإجراءات -->
                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                    <Button x:Name="BtnAddObligation" Content="➕ إضافة التزام جديد" Style="{StaticResource ModernButton3D}"
                            Margin="0,0,12,0" Click="BtnAddObligation_Click"/>
                    <Button x:Name="BtnRefresh" Content="🔄 تحديث" Style="{StaticResource SecondaryButton3D}"
                            Margin="0,0,12,0" Click="BtnRefresh_Click"/>
                    <Button x:Name="BtnImport" Content="📥 استيراد" Style="{StaticResource ModernButton3D}"
                            Margin="0,0,12,0" Click="BtnImport_Click">
                        <Button.Background>
                            <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                <GradientStop Color="#8B5CF6" Offset="0"/>
                                <GradientStop Color="#7C3AED" Offset="1"/>
                            </LinearGradientBrush>
                        </Button.Background>
                    </Button>
                    <Button x:Name="BtnExport" Content="📤 تصدير" Style="{StaticResource SuccessButton3D}"
                            Margin="0,0,12,0" Click="BtnExport_Click"/>
                    <Button x:Name="BtnPrint" Content="🖨️ طباعة" Style="{StaticResource WarningButton3D}"
                            Click="BtnPrint_Click"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- المحتوى الرئيسي المحسن -->
        <Border Grid.Row="1" Style="{StaticResource Card3D}" Margin="16">
            <Border.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                    <GradientStop Color="White" Offset="0"/>
                    <GradientStop Color="#F8FAFC" Offset="1"/>
                </LinearGradientBrush>
            </Border.Background>
            <Border.RenderTransform>
                <TranslateTransform/>
            </Border.RenderTransform>
            <Border.Triggers>
                <EventTrigger RoutedEvent="Loaded">
                    <BeginStoryboard Storyboard="{StaticResource FadeInAnimation}"/>
                </EventTrigger>
            </Border.Triggers>

            <Grid Margin="24">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- شريط البحث والفلترة -->
                <Border Grid.Row="0" Background="#F8FAFC" CornerRadius="8" Padding="16" Margin="0,0,0,16">
                    <StackPanel>
                        <!-- الصف الأول: البحث والفلاتر الأساسية -->
                        <Grid Margin="0,0,0,12">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBox x:Name="TxtSearch" Grid.Column="0"
                                     FontSize="14" Padding="12,8"
                                     Background="White" BorderBrush="#D1D5DB" BorderThickness="1"
                                     Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                                     VerticalAlignment="Center" Margin="0,0,12,0">
                                <TextBox.Style>
                                    <Style TargetType="TextBox">
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate TargetType="TextBox">
                                                    <Border Background="{TemplateBinding Background}"
                                                            BorderBrush="{TemplateBinding BorderBrush}"
                                                            BorderThickness="{TemplateBinding BorderThickness}"
                                                            CornerRadius="6">
                                                        <Grid>
                                                            <TextBlock Text="🔍 البحث في الالتزامات..."
                                                                       Foreground="#9CA3AF"
                                                                       Margin="12,0,0,0"
                                                                       VerticalAlignment="Center"
                                                                       IsHitTestVisible="False">
                                                                <TextBlock.Style>
                                                                    <Style TargetType="TextBlock">
                                                                        <Setter Property="Visibility" Value="Collapsed"/>
                                                                        <Style.Triggers>
                                                                            <DataTrigger Binding="{Binding Text, RelativeSource={RelativeSource TemplatedParent}}" Value="">
                                                                                <Setter Property="Visibility" Value="Visible"/>
                                                                            </DataTrigger>
                                                                        </Style.Triggers>
                                                                    </Style>
                                                                </TextBlock.Style>
                                                            </TextBlock>
                                                            <ScrollViewer x:Name="PART_ContentHost" Margin="12,0,0,0" VerticalAlignment="Center"/>
                                                        </Grid>
                                                    </Border>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Style>
                                </TextBox.Style>
                            </TextBox>

                            <ComboBox x:Name="CmbStatusFilter" Grid.Column="1"
                                      Width="150" FontSize="14" Padding="12,8"
                                      Background="White" BorderBrush="#D1D5DB" BorderThickness="1"
                                      SelectedValuePath="Content" VerticalAlignment="Center" Margin="0,0,12,0"
                                      SelectionChanged="CmbStatusFilter_SelectionChanged">
                                <ComboBoxItem Content="جميع الحالات" IsSelected="True"/>
                                <ComboBoxItem Content="نشط"/>
                                <ComboBoxItem Content="مكتمل"/>
                                <ComboBoxItem Content="متأخر"/>
                                <ComboBoxItem Content="قريب الاستحقاق"/>
                            </ComboBox>

                            <ComboBox x:Name="CmbTypeFilter" Grid.Column="2"
                                      Width="150" FontSize="14" Padding="12,8"
                                      Background="White" BorderBrush="#D1D5DB" BorderThickness="1"
                                      SelectedValuePath="Content" VerticalAlignment="Center" Margin="0,0,12,0"
                                      SelectionChanged="CmbTypeFilter_SelectionChanged">
                                <ComboBoxItem Content="جميع الأنواع" IsSelected="True"/>
                                <ComboBoxItem Content="التزام مالي"/>
                                <ComboBoxItem Content="التزام قانوني"/>
                                <ComboBoxItem Content="التزام إداري"/>
                                <ComboBoxItem Content="التزام تجاري"/>
                                <ComboBoxItem Content="التزام حكومي"/>
                                <ComboBoxItem Content="التزام تأميني"/>
                            </ComboBox>

                            <Button x:Name="BtnSearch" Grid.Column="3" Content="🔍 بحث"
                                    Style="{StaticResource ModernButton}" Click="BtnSearch_Click"/>
                        </Grid>

                        <!-- الصف الثاني: فلاتر التاريخ والمبلغ -->
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0" Text="من تاريخ:" VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <DatePicker x:Name="DateFrom" Grid.Column="1" FontSize="12" Margin="0,0,16,0"
                                       SelectedDateChanged="DateFilter_Changed"/>

                            <TextBlock Grid.Column="2" Text="إلى تاريخ:" VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <DatePicker x:Name="DateTo" Grid.Column="3" FontSize="12" Margin="0,0,16,0"
                                       SelectedDateChanged="DateFilter_Changed"/>

                            <Button x:Name="BtnClearFilters" Grid.Column="4" Content="🗑️ مسح الفلاتر"
                                    Style="{StaticResource SecondaryButton}" FontSize="12" Padding="8,4"
                                    Margin="0,0,8,0" Click="BtnClearFilters_Click"/>

                            <Button x:Name="BtnAdvancedSearch" Grid.Column="5" Content="🔧 بحث متقدم"
                                    Style="{StaticResource ModernButton}" FontSize="12" Padding="8,4"
                                    Background="#8B5CF6" Click="BtnAdvancedSearch_Click"/>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- جدول الالتزامات المحسن -->
                <dxg:GridControl x:Name="ObligationsGrid" Grid.Row="1"
                                 AutoGenerateColumns="False"
                                 EnableSmartColumnsGeneration="True"
                                 SelectionMode="Row"
                                 ShowBorder="False">

                    <dxg:GridControl.Columns>
                        <dxg:GridColumn FieldName="Name" Header="📋 اسم الالتزام" Width="250" MinWidth="200">
                            <dxg:GridColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                                        <Ellipse Width="8" Height="8" Margin="0,0,8,0">
                                            <Ellipse.Fill>
                                                <SolidColorBrush Color="{Binding StatusColor}"/>
                                            </Ellipse.Fill>
                                        </Ellipse>
                                        <TextBlock Text="{Binding Name}" FontWeight="SemiBold" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </DataTemplate>
                            </dxg:GridColumn.CellTemplate>
                        </dxg:GridColumn>

                        <dxg:GridColumn FieldName="Type" Header="🏷️ نوع الالتزام" Width="180">
                            <dxg:GridColumn.CellTemplate>
                                <DataTemplate>
                                    <Border Background="#EEF2FF" CornerRadius="12" Padding="8,4" HorizontalAlignment="Center">
                                        <TextBlock Text="{Binding Type}" FontSize="12" FontWeight="Medium"
                                                   Foreground="#4338CA" HorizontalAlignment="Center"/>
                                    </Border>
                                </DataTemplate>
                            </dxg:GridColumn.CellTemplate>
                        </dxg:GridColumn>

                        <dxg:GridColumn FieldName="Duration" Header="⏱️ مدة الالتزام" Width="140"/>

                        <dxg:GridColumn FieldName="DueDate" Header="📅 تاريخ الاستحقاق" Width="160">
                            <dxg:GridColumn.EditSettings>
                                <dxe:DateEditSettings DisplayFormat="dd/MM/yyyy"/>
                            </dxg:GridColumn.EditSettings>
                            <dxg:GridColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding DueDate, StringFormat=dd/MM/yyyy}"
                                               FontWeight="Medium" VerticalAlignment="Center"/>
                                </DataTemplate>
                            </dxg:GridColumn.CellTemplate>
                        </dxg:GridColumn>

                        <dxg:GridColumn FieldName="DaysRemaining" Header="⏰ الأيام المتبقية" Width="140">
                            <dxg:GridColumn.CellTemplate>
                                <DataTemplate>
                                    <Border CornerRadius="8" Padding="6,3" HorizontalAlignment="Center">
                                        <Border.Style>
                                            <Style TargetType="Border">
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding IsOverdue}" Value="True">
                                                        <Setter Property="Background" Value="#FEE2E2"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding IsNearDue}" Value="True">
                                                        <Setter Property="Background" Value="#FEF3C7"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding IsOverdue}" Value="False">
                                                        <DataTrigger.Setters>
                                                            <Setter Property="Background" Value="#D1FAE5"/>
                                                        </DataTrigger.Setters>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </Border.Style>
                                        <TextBlock FontWeight="Bold" HorizontalAlignment="Center">
                                            <TextBlock.Style>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="Text" Value="{Binding DaysRemaining}"/>
                                                    <Setter Property="Foreground" Value="#059669"/>
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding IsOverdue}" Value="True">
                                                            <Setter Property="Text" Value="متأخر"/>
                                                            <Setter Property="Foreground" Value="#DC2626"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding IsNearDue}" Value="True">
                                                            <Setter Property="Foreground" Value="#D97706"/>
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </TextBlock.Style>
                                        </TextBlock>
                                    </Border>
                                </DataTemplate>
                            </dxg:GridColumn.CellTemplate>
                        </dxg:GridColumn>

                        <dxg:GridColumn FieldName="Amount" Header="💰 المبلغ" Width="140">
                            <dxg:GridColumn.EditSettings>
                                <dxe:SpinEditSettings DisplayFormat="N0"/>
                            </dxg:GridColumn.EditSettings>
                            <dxg:GridColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding Amount, StringFormat='{}{0:N0} ريال'}"
                                               FontWeight="SemiBold" Foreground="#059669" VerticalAlignment="Center"/>
                                </DataTemplate>
                            </dxg:GridColumn.CellTemplate>
                        </dxg:GridColumn>

                        <dxg:GridColumn FieldName="StatusText" Header="📊 الحالة" Width="130">
                            <dxg:GridColumn.CellTemplate>
                                <DataTemplate>
                                    <Border CornerRadius="12" Padding="8,4" HorizontalAlignment="Center">
                                        <Border.Style>
                                            <Style TargetType="Border">
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding Status}" Value="نشط">
                                                        <Setter Property="Background" Value="#D1FAE5"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Status}" Value="مكتمل">
                                                        <Setter Property="Background" Value="#DBEAFE"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </Border.Style>
                                        <TextBlock Text="{Binding StatusText}" FontSize="12" FontWeight="Bold"
                                                   HorizontalAlignment="Center">
                                            <TextBlock.Style>
                                                <Style TargetType="TextBlock">
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding Status}" Value="نشط">
                                                            <Setter Property="Foreground" Value="#059669"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Status}" Value="مكتمل">
                                                            <Setter Property="Foreground" Value="#2563EB"/>
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </TextBlock.Style>
                                        </TextBlock>
                                    </Border>
                                </DataTemplate>
                            </dxg:GridColumn.CellTemplate>
                        </dxg:GridColumn>
                    </dxg:GridControl.Columns>

                    <dxg:GridControl.View>
                        <dxg:TableView ShowGroupPanel="False"
                                       AutoWidth="True"
                                       AllowEditing="False"
                                       ShowIndicator="True"
                                       ShowVerticalLines="False"
                                       ShowHorizontalLines="True"
                                       RowDoubleClick="TableView_RowDoubleClick"
                                       AlternatingRowBackground="#F8FAFC"
                                       FadeSelectionOnLostFocus="False"
                                       NavigationStyle="Row"
                                       AllowColumnFiltering="True"
                                       AllowSorting="True"
                                       ShowAutoFilterRow="True"/>
                    </dxg:GridControl.View>

                    <dxg:GridControl.ContextMenu>
                        <ContextMenu>
                            <MenuItem Header="✏️ تعديل" Click="EditObligation_Click">
                                <MenuItem.Icon>
                                    <TextBlock Text="✏️" FontSize="14"/>
                                </MenuItem.Icon>
                            </MenuItem>
                            <MenuItem Header="👁️ عرض التفاصيل" Click="ViewObligation_Click">
                                <MenuItem.Icon>
                                    <TextBlock Text="👁️" FontSize="14"/>
                                </MenuItem.Icon>
                            </MenuItem>
                            <Separator/>
                            <MenuItem Header="✅ تمييز كمكتمل" Click="MarkAsCompleted_Click">
                                <MenuItem.Icon>
                                    <TextBlock Text="✅" FontSize="14"/>
                                </MenuItem.Icon>
                            </MenuItem>
                            <MenuItem Header="🔄 إعادة تفعيل" Click="ReactivateObligation_Click">
                                <MenuItem.Icon>
                                    <TextBlock Text="🔄" FontSize="14"/>
                                </MenuItem.Icon>
                            </MenuItem>
                            <Separator/>
                            <MenuItem Header="🗑️ حذف" Click="DeleteObligation_Click">
                                <MenuItem.Icon>
                                    <TextBlock Text="🗑️" FontSize="14"/>
                                </MenuItem.Icon>
                            </MenuItem>
                        </ContextMenu>
                    </dxg:GridControl.ContextMenu>
                </dxg:GridControl>
            </Grid>
        </Border>

        <!-- شريط الحالة المحسن -->
        <Border Grid.Row="2" Style="{StaticResource Card3D}" Margin="16,8,16,16">
            <Border.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                    <GradientStop Color="#F8FAFC" Offset="0"/>
                    <GradientStop Color="#E2E8F0" Offset="1"/>
                </LinearGradientBrush>
            </Border.Background>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- إحصائيات محسنة -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <!-- إجمالي الالتزامات -->
                    <Border Background="#EEF2FF" CornerRadius="12" Padding="12,6" Margin="0,0,16,0">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="📊" FontSize="16" Margin="0,0,8,0"/>
                            <TextBlock Text="إجمالي: " FontSize="14" FontWeight="Medium" Foreground="#4338CA"/>
                            <TextBlock x:Name="TxtTotalObligations" Text="0" FontSize="16" FontWeight="Bold" Foreground="#4338CA"/>
                        </StackPanel>
                    </Border>

                    <!-- الالتزامات النشطة -->
                    <Border Background="#D1FAE5" CornerRadius="12" Padding="12,6" Margin="0,0,16,0">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="✅" FontSize="16" Margin="0,0,8,0"/>
                            <TextBlock Text="نشط: " FontSize="14" FontWeight="Medium" Foreground="#059669"/>
                            <TextBlock x:Name="TxtActiveObligations" Text="0" FontSize="16" FontWeight="Bold" Foreground="#059669"/>
                        </StackPanel>
                    </Border>

                    <!-- الالتزامات المتأخرة -->
                    <Border Background="#FEE2E2" CornerRadius="12" Padding="12,6">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="⚠️" FontSize="16" Margin="0,0,8,0"/>
                            <TextBlock Text="متأخر: " FontSize="14" FontWeight="Medium" Foreground="#DC2626"/>
                            <TextBlock x:Name="TxtOverdueObligations" Text="0" FontSize="16" FontWeight="Bold" Foreground="#DC2626"/>
                        </StackPanel>
                    </Border>
                </StackPanel>

                <!-- معلومات التحديث -->
                <Border Grid.Column="1" Background="#F3F4F6" CornerRadius="8" Padding="12,6">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="🕒" FontSize="14" Margin="0,0,8,0"/>
                        <TextBlock x:Name="TxtLastUpdate" Text="آخر تحديث: --"
                                   FontSize="12" Foreground="#6B7280" VerticalAlignment="Center"/>
                    </StackPanel>
                </Border>
            </Grid>
        </Border>
    </Grid>
</Window>
