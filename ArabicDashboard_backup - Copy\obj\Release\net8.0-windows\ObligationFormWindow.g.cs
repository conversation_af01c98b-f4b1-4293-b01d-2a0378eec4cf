﻿#pragma checksum "..\..\..\ObligationFormWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "33F91506A57071F6D0C0200D0439004744459F80"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using DevExpress.Core;
using DevExpress.Xpf.Core;
using DevExpress.Xpf.Core.ConditionalFormatting;
using DevExpress.Xpf.Core.DataSources;
using DevExpress.Xpf.Core.Serialization;
using DevExpress.Xpf.Core.ServerMode;
using DevExpress.Xpf.DXBinding;
using DevExpress.Xpf.Data;
using DevExpress.Xpf.Editors;
using DevExpress.Xpf.Editors.DataPager;
using DevExpress.Xpf.Editors.DateNavigator;
using DevExpress.Xpf.Editors.ExpressionEditor;
using DevExpress.Xpf.Editors.Filtering;
using DevExpress.Xpf.Editors.Flyout;
using DevExpress.Xpf.Editors.Popups;
using DevExpress.Xpf.Editors.Popups.Calendar;
using DevExpress.Xpf.Editors.RangeControl;
using DevExpress.Xpf.Editors.Settings;
using DevExpress.Xpf.Editors.Settings.Extension;
using DevExpress.Xpf.Editors.Validation;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace ArabicDashboard {
    
    
    /// <summary>
    /// ObligationFormWindow
    /// </summary>
    public partial class ObligationFormWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 182 "..\..\..\ObligationFormWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTitle;
        
        #line default
        #line hidden
        
        
        #line 211 "..\..\..\ObligationFormWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtName;
        
        #line default
        #line hidden
        
        
        #line 216 "..\..\..\ObligationFormWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.ComboBoxEdit CmbType;
        
        #line default
        #line hidden
        
        
        #line 236 "..\..\..\ObligationFormWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.ComboBoxEdit CmbDuration;
        
        #line default
        #line hidden
        
        
        #line 263 "..\..\..\ObligationFormWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.DateEdit DateDue;
        
        #line default
        #line hidden
        
        
        #line 271 "..\..\..\ObligationFormWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.SpinEdit NumAmount;
        
        #line default
        #line hidden
        
        
        #line 293 "..\..\..\ObligationFormWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.CheckEdit ChkReminderEnabled;
        
        #line default
        #line hidden
        
        
        #line 306 "..\..\..\ObligationFormWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.SpinEdit NumReminderDays;
        
        #line default
        #line hidden
        
        
        #line 317 "..\..\..\ObligationFormWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.MemoEdit TxtNotes;
        
        #line default
        #line hidden
        
        
        #line 323 "..\..\..\ObligationFormWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border InfoPanel;
        
        #line default
        #line hidden
        
        
        #line 336 "..\..\..\ObligationFormWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtCreatedDate;
        
        #line default
        #line hidden
        
        
        #line 339 "..\..\..\ObligationFormWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtCurrentStatus;
        
        #line default
        #line hidden
        
        
        #line 344 "..\..\..\ObligationFormWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtUpdatedDate;
        
        #line default
        #line hidden
        
        
        #line 347 "..\..\..\ObligationFormWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtDaysRemaining;
        
        #line default
        #line hidden
        
        
        #line 369 "..\..\..\ObligationFormWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnSave;
        
        #line default
        #line hidden
        
        
        #line 371 "..\..\..\ObligationFormWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnSaveAndNew;
        
        #line default
        #line hidden
        
        
        #line 373 "..\..\..\ObligationFormWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnCancel;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/ArabicDashboard;component/obligationformwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\ObligationFormWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TxtTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.TxtName = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.CmbType = ((DevExpress.Xpf.Editors.ComboBoxEdit)(target));
            return;
            case 4:
            this.CmbDuration = ((DevExpress.Xpf.Editors.ComboBoxEdit)(target));
            return;
            case 5:
            this.DateDue = ((DevExpress.Xpf.Editors.DateEdit)(target));
            return;
            case 6:
            this.NumAmount = ((DevExpress.Xpf.Editors.SpinEdit)(target));
            return;
            case 7:
            this.ChkReminderEnabled = ((DevExpress.Xpf.Editors.CheckEdit)(target));
            return;
            case 8:
            this.NumReminderDays = ((DevExpress.Xpf.Editors.SpinEdit)(target));
            return;
            case 9:
            this.TxtNotes = ((DevExpress.Xpf.Editors.MemoEdit)(target));
            return;
            case 10:
            this.InfoPanel = ((System.Windows.Controls.Border)(target));
            return;
            case 11:
            this.TxtCreatedDate = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.TxtCurrentStatus = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.TxtUpdatedDate = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.TxtDaysRemaining = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.BtnSave = ((System.Windows.Controls.Button)(target));
            
            #line 370 "..\..\..\ObligationFormWindow.xaml"
            this.BtnSave.Click += new System.Windows.RoutedEventHandler(this.BtnSave_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.BtnSaveAndNew = ((System.Windows.Controls.Button)(target));
            
            #line 372 "..\..\..\ObligationFormWindow.xaml"
            this.BtnSaveAndNew.Click += new System.Windows.RoutedEventHandler(this.BtnSaveAndNew_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.BtnCancel = ((System.Windows.Controls.Button)(target));
            
            #line 374 "..\..\..\ObligationFormWindow.xaml"
            this.BtnCancel.Click += new System.Windows.RoutedEventHandler(this.BtnCancel_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

