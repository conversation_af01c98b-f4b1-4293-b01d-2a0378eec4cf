﻿#pragma checksum "..\..\..\SettingsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "133E9ADAFB6E4D73AEFA03F15E9FF668CD555707"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using DevExpress.Xpf.DXBinding;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace ArabicDashboard {
    
    
    /// <summary>
    /// SettingsWindow
    /// </summary>
    public partial class SettingsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 87 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AutoStartCheckBox;
        
        #line default
        #line hidden
        
        
        #line 91 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox NotificationsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 96 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox PrayerNotificationsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 115 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DatabasePathTextBox;
        
        #line default
        #line hidden
        
        
        #line 147 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ThemeComboBox;
        
        #line default
        #line hidden
        
        
        #line 182 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AnimationsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 187 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ThreeDEffectsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 192 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox GradientBackgroundCheckBox;
        
        #line default
        #line hidden
        
        
        #line 197 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ShadowEffectsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 206 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider WindowOpacitySlider;
        
        #line default
        #line hidden
        
        
        #line 214 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock OpacityValueText;
        
        #line default
        #line hidden
        
        
        #line 245 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox FastLoadingCheckBox;
        
        #line default
        #line hidden
        
        
        #line 250 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox CacheDataCheckBox;
        
        #line default
        #line hidden
        
        
        #line 255 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AutoSaveCheckBox;
        
        #line default
        #line hidden
        
        
        #line 264 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox AutoSaveIntervalComboBox;
        
        #line default
        #line hidden
        
        
        #line 279 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PageSizeComboBox;
        
        #line default
        #line hidden
        
        
        #line 300 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AutoArchiveCheckBox;
        
        #line default
        #line hidden
        
        
        #line 309 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ArchiveDayComboBox;
        
        #line default
        #line hidden
        
        
        #line 318 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox CompressArchiveCheckBox;
        
        #line default
        #line hidden
        
        
        #line 323 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EncryptArchiveCheckBox;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/ArabicDashboard;component/settingswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\SettingsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.AutoStartCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 2:
            this.NotificationsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 3:
            this.PrayerNotificationsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 4:
            this.DatabasePathTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            
            #line 121 "..\..\..\SettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BrowseDatabasePath_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            
            #line 127 "..\..\..\SettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BackupDatabase_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            
            #line 130 "..\..\..\SettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RestoreDatabase_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.ThemeComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 150 "..\..\..\SettingsWindow.xaml"
            this.ThemeComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.ThemeComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 9:
            this.AnimationsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 10:
            this.ThreeDEffectsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 11:
            this.GradientBackgroundCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 12:
            this.ShadowEffectsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 13:
            this.WindowOpacitySlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 14:
            this.OpacityValueText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            
            #line 224 "..\..\..\SettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PreviewTheme_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            
            #line 228 "..\..\..\SettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ResetTheme_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            
            #line 232 "..\..\..\SettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SaveCustomTheme_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            this.FastLoadingCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 19:
            this.CacheDataCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 20:
            this.AutoSaveCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 21:
            this.AutoSaveIntervalComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 22:
            this.PageSizeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 23:
            this.AutoArchiveCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 24:
            this.ArchiveDayComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 25:
            this.CompressArchiveCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 26:
            this.EncryptArchiveCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 27:
            
            #line 354 "..\..\..\SettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SaveSettings_Click);
            
            #line default
            #line hidden
            return;
            case 28:
            
            #line 358 "..\..\..\SettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ResetSettings_Click);
            
            #line default
            #line hidden
            return;
            case 29:
            
            #line 362 "..\..\..\SettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Cancel_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

