<Window x:Class="ArabicDashboard.ObligationFormWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core"
        xmlns:dxe="http://schemas.devexpress.com/winfx/2008/xaml/editors"
        Title="📋 نموذج الالتزام"
        Height="800" Width="700"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        ResizeMode="CanResize"
        MinHeight="700" MinWidth="600"
        dx:ThemeManager.ThemeName="Office2019Colorful"
        AllowsTransparency="False"
        WindowStyle="SingleBorderWindow">

    <Window.Background>
        <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#F8FAFC" Offset="0"/>
            <GradientStop Color="#E2E8F0" Offset="1"/>
        </LinearGradientBrush>
    </Window.Background>

    <Window.Effect>
        <DropShadowEffect Color="Black" Direction="315" ShadowDepth="8" Opacity="0.25" BlurRadius="15"/>
    </Window.Effect>

    <Window.Resources>
        <!-- أنماط الأزرار -->
        <Style x:Key="ModernButton" TargetType="Button">
            <Setter Property="Background" Value="#3B82F6"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,12"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border"
                                Background="{TemplateBinding Background}"
                                CornerRadius="8"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#2563EB"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#1D4ED8"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter TargetName="border" Property="Background" Value="#9CA3AF"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SecondaryButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
            <Setter Property="Background" Value="#6B7280"/>
        </Style>

        <Style x:Key="DangerButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
            <Setter Property="Background" Value="#EF4444"/>
        </Style>

        <Style x:Key="SuccessButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
            <Setter Property="Background" Value="#10B981"/>
        </Style>

        <!-- أنماط البطاقات ثلاثية الأبعاد -->
        <Style x:Key="FormCard" TargetType="Border">
            <Setter Property="Background">
                <Setter.Value>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                        <GradientStop Color="White" Offset="0"/>
                        <GradientStop Color="#F8FAFC" Offset="1"/>
                    </LinearGradientBrush>
                </Setter.Value>
            </Setter>
            <Setter Property="CornerRadius" Value="16"/>
            <Setter Property="Padding" Value="32"/>
            <Setter Property="Margin" Value="16"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Black" Direction="270" ShadowDepth="12" Opacity="0.2" BlurRadius="30"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- أنماط التسميات المحسنة -->
        <Style x:Key="FieldLabel" TargetType="TextBlock">
            <Setter Property="FontSize" Value="15"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="#1F2937"/>
            <Setter Property="Margin" Value="0,0,0,10"/>
        </Style>

        <Style x:Key="RequiredFieldLabel" TargetType="TextBlock" BasedOn="{StaticResource FieldLabel}">
            <Setter Property="Foreground" Value="#DC2626"/>
        </Style>

        <Style x:Key="SectionHeader" TargetType="TextBlock">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#374151"/>
            <Setter Property="Margin" Value="0,0,0,16"/>
        </Style>

        <!-- أنماط الحقول المحسنة -->
        <Style x:Key="ModernTextBox" TargetType="TextBox">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Padding" Value="16,12"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#D1D5DB"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border x:Name="border"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="12"
                                Padding="{TemplateBinding Padding}">
                            <Border.Effect>
                                <DropShadowEffect Color="Black" Direction="270" ShadowDepth="2" Opacity="0.1" BlurRadius="8"/>
                            </Border.Effect>
                            <ScrollViewer x:Name="PART_ContentHost" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter TargetName="border" Property="BorderBrush" Value="#4F46E5"/>
                                <Setter TargetName="border" Property="Effect">
                                    <Setter.Value>
                                        <DropShadowEffect Color="#4F46E5" Direction="270" ShadowDepth="0" Opacity="0.3" BlurRadius="12"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- أنماط الانتقالات -->
        <Storyboard x:Key="SlideInAnimation">
            <DoubleAnimation Storyboard.TargetProperty="Opacity" From="0" To="1" Duration="0:0:0.8"/>
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)"
                           From="30" To="0" Duration="0:0:0.8">
                <DoubleAnimation.EasingFunction>
                    <CubicEase EasingMode="EaseOut"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
        </Storyboard>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- الشريط العلوي المحسن -->
        <Border Grid.Row="0" Padding="32,24">
            <Border.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                    <GradientStop Color="#667EEA" Offset="0"/>
                    <GradientStop Color="#764BA2" Offset="1"/>
                </LinearGradientBrush>
            </Border.Background>
            <Border.Effect>
                <DropShadowEffect Color="Black" Direction="270" ShadowDepth="8" Opacity="0.2" BlurRadius="20"/>
            </Border.Effect>
            <StackPanel>
                <TextBlock x:Name="TxtTitle" Text="📋 إضافة التزام جديد" FontSize="26" FontWeight="Bold" Foreground="White"
                           Margin="0,0,0,8">
                    <TextBlock.Effect>
                        <DropShadowEffect Color="Black" Direction="270" ShadowDepth="2" Opacity="0.5" BlurRadius="4"/>
                    </TextBlock.Effect>
                </TextBlock>
                <TextBlock Text="أدخل تفاصيل الالتزام الجديد بدقة لضمان المتابعة الصحيحة"
                           FontSize="16" Foreground="#E2E8F0" FontWeight="Medium"/>
            </StackPanel>
        </Border>

        <!-- المحتوى الرئيسي المحسن -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="0">
            <Border Style="{StaticResource FormCard}">
                <Border.RenderTransform>
                    <TranslateTransform/>
                </Border.RenderTransform>
                <Border.Triggers>
                    <EventTrigger RoutedEvent="Loaded">
                        <BeginStoryboard Storyboard="{StaticResource SlideInAnimation}"/>
                    </EventTrigger>
                </Border.Triggers>

                <StackPanel>
                    <!-- قسم المعلومات الأساسية -->
                    <TextBlock Text="📋 المعلومات الأساسية" Style="{StaticResource SectionHeader}"/>

                    <!-- اسم الالتزام -->
                    <TextBlock Text="اسم الالتزام *" Style="{StaticResource RequiredFieldLabel}"/>
                    <TextBox x:Name="TxtName" Style="{StaticResource ModernTextBox}"
                             ToolTip="أدخل اسماً وصفياً واضحاً للالتزام"/>

                    <!-- نوع الالتزام -->
                    <TextBlock Text="نوع الالتزام *" Style="{StaticResource RequiredFieldLabel}"/>
                    <dxe:ComboBoxEdit x:Name="CmbType" FontSize="14" Padding="16,12"
                                      Background="White" BorderBrush="#D1D5DB" BorderThickness="2"
                                      Margin="0,0,0,20" IsTextEditable="True"
                                      ToolTip="اختر نوع الالتزام أو أدخل نوعاً جديداً">
                        <dxe:ComboBoxEdit.StyleSettings>
                            <dxe:ComboBoxStyleSettings/>
                        </dxe:ComboBoxEdit.StyleSettings>
                        <dxe:ComboBoxEditItem Content="💰 التزام مالي"/>
                        <dxe:ComboBoxEditItem Content="⚖️ التزام قانوني"/>
                        <dxe:ComboBoxEditItem Content="📄 التزام إداري"/>
                        <dxe:ComboBoxEditItem Content="🏢 التزام تجاري"/>
                        <dxe:ComboBoxEditItem Content="👤 التزام شخصي"/>
                        <dxe:ComboBoxEditItem Content="🏛️ التزام حكومي"/>
                        <dxe:ComboBoxEditItem Content="📝 التزام تعاقدي"/>
                        <dxe:ComboBoxEditItem Content="🛡️ التزام تأميني"/>
                        <dxe:ComboBoxEditItem Content="🔧 أخرى"/>
                    </dxe:ComboBoxEdit>

                    <!-- مدة الالتزام -->
                    <TextBlock Text="مدة الالتزام *" Style="{StaticResource RequiredFieldLabel}"/>
                    <dxe:ComboBoxEdit x:Name="CmbDuration" FontSize="14" Padding="16,12"
                                      Background="White" BorderBrush="#D1D5DB" BorderThickness="2"
                                      Margin="0,0,0,20" IsTextEditable="True"
                                      ToolTip="حدد تكرار الالتزام">
                        <dxe:ComboBoxEditItem Content="📅 يومي"/>
                        <dxe:ComboBoxEditItem Content="📅 أسبوعي"/>
                        <dxe:ComboBoxEditItem Content="📅 شهري"/>
                        <dxe:ComboBoxEditItem Content="📅 ربع سنوي"/>
                        <dxe:ComboBoxEditItem Content="📅 نصف سنوي"/>
                        <dxe:ComboBoxEditItem Content="📅 سنوي"/>
                        <dxe:ComboBoxEditItem Content="🔄 مرة واحدة"/>
                        <dxe:ComboBoxEditItem Content="⚡ حسب الحاجة"/>
                    </dxe:ComboBoxEdit>

                    <!-- قسم التواريخ والمبالغ -->
                    <TextBlock Text="📅 التواريخ والمبالغ" Style="{StaticResource SectionHeader}" Margin="0,24,0,16"/>

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="16"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- تاريخ الاستحقاق -->
                        <StackPanel Grid.Column="0">
                            <TextBlock Text="تاريخ الاستحقاق *" Style="{StaticResource RequiredFieldLabel}"/>
                            <dxe:DateEdit x:Name="DateDue" FontSize="14" Padding="16,12"
                                          Background="White" BorderBrush="#D1D5DB" BorderThickness="2"
                                          Margin="0,0,0,20" ToolTip="حدد تاريخ استحقاق الالتزام"/>
                        </StackPanel>

                        <!-- المبلغ -->
                        <StackPanel Grid.Column="2">
                            <TextBlock Text="المبلغ (ريال سعودي)" Style="{StaticResource FieldLabel}"/>
                            <dxe:SpinEdit x:Name="NumAmount" FontSize="14" Padding="16,12"
                                          Background="White" BorderBrush="#D1D5DB" BorderThickness="2"
                                          Margin="0,0,0,20" MinValue="0" MaxValue="999999999"
                                          ToolTip="أدخل المبلغ المطلوب إن وجد"/>
                        </StackPanel>
                    </Grid>

                    <!-- قسم إعدادات التنبيه -->
                    <TextBlock Text="🔔 إعدادات التنبيه" Style="{StaticResource SectionHeader}" Margin="0,24,0,16"/>

                    <Border CornerRadius="16" Padding="24" Margin="0,0,0,24">
                        <Border.Background>
                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                <GradientStop Color="#FEF3C7" Offset="0"/>
                                <GradientStop Color="#FDE68A" Offset="1"/>
                            </LinearGradientBrush>
                        </Border.Background>
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Direction="270" ShadowDepth="4" Opacity="0.1" BlurRadius="12"/>
                        </Border.Effect>

                        <StackPanel>
                            <dxe:CheckEdit x:Name="ChkReminderEnabled" Content="🔔 تفعيل التنبيه قبل تاريخ الاستحقاق"
                                           FontSize="16" FontWeight="SemiBold" Foreground="#92400E"
                                           IsChecked="True" Margin="0,0,0,16"/>

                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="120"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <TextBlock Grid.Column="0" Text="📅 التنبيه قبل" FontSize="15" FontWeight="Medium"
                                           Foreground="#92400E" VerticalAlignment="Center" Margin="0,0,12,0"/>
                                <dxe:SpinEdit x:Name="NumReminderDays" Grid.Column="1" FontSize="14" Padding="12,8"
                                              Background="White" BorderBrush="#D97706" BorderThickness="2"
                                              Value="7" MinValue="1" MaxValue="365" Margin="0,0,12,0"/>
                                <TextBlock Grid.Column="2" Text="يوم من تاريخ الاستحقاق" FontSize="15" FontWeight="Medium"
                                           Foreground="#92400E" VerticalAlignment="Center"/>
                            </Grid>
                        </StackPanel>
                    </Border>

                    <!-- قسم الملاحظات -->
                    <TextBlock Text="📝 الملاحظات والتفاصيل الإضافية" Style="{StaticResource SectionHeader}" Margin="0,24,0,16"/>
                    <dxe:MemoEdit x:Name="TxtNotes" FontSize="14" Height="120"
                                  Background="White" BorderBrush="#D1D5DB" BorderThickness="2"
                                  Margin="0,0,0,20"
                                  ToolTip="أضف أي ملاحظات أو تفاصيل إضافية حول الالتزام"/>

                    <!-- معلومات إضافية (في وضع العرض فقط) -->
                    <Border x:Name="InfoPanel" Background="#EEF2FF" CornerRadius="8" Padding="16" 
                            Margin="0,0,0,16" Visibility="Collapsed">
                        <StackPanel>
                            <TextBlock Text="ℹ️ معلومات إضافية" FontSize="16" FontWeight="Bold" Foreground="#3730A3" Margin="0,0,0,12"/>
                            
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <StackPanel Grid.Column="0" Margin="0,0,16,0">
                                    <TextBlock Text="تاريخ الإنشاء:" FontSize="12" Foreground="#6B7280" Margin="0,0,0,4"/>
                                    <TextBlock x:Name="TxtCreatedDate" Text="--" FontSize="14" FontWeight="Medium" Foreground="#374151" Margin="0,0,0,8"/>
                                    
                                    <TextBlock Text="الحالة الحالية:" FontSize="12" Foreground="#6B7280" Margin="0,0,0,4"/>
                                    <TextBlock x:Name="TxtCurrentStatus" Text="--" FontSize="14" FontWeight="Medium" Foreground="#374151"/>
                                </StackPanel>
                                
                                <StackPanel Grid.Column="1">
                                    <TextBlock Text="آخر تحديث:" FontSize="12" Foreground="#6B7280" Margin="0,0,0,4"/>
                                    <TextBlock x:Name="TxtUpdatedDate" Text="--" FontSize="14" FontWeight="Medium" Foreground="#374151" Margin="0,0,0,8"/>
                                    
                                    <TextBlock Text="الأيام المتبقية:" FontSize="12" Foreground="#6B7280" Margin="0,0,0,4"/>
                                    <TextBlock x:Name="TxtDaysRemaining" Text="--" FontSize="14" FontWeight="Bold" Foreground="#EF4444"/>
                                </StackPanel>
                            </Grid>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </Border>
        </ScrollViewer>

        <!-- شريط الأزرار المحسن -->
        <Border Grid.Row="2" Padding="32,20">
            <Border.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                    <GradientStop Color="#F8FAFC" Offset="0"/>
                    <GradientStop Color="#E2E8F0" Offset="1"/>
                </LinearGradientBrush>
            </Border.Background>
            <Border.Effect>
                <DropShadowEffect Color="Black" Direction="90" ShadowDepth="4" Opacity="0.1" BlurRadius="12"/>
            </Border.Effect>

            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="BtnSave" Content="💾 حفظ الالتزام" Style="{StaticResource SuccessButton}"
                        Margin="0,0,16,0" Click="BtnSave_Click" MinWidth="140"/>
                <Button x:Name="BtnSaveAndNew" Content="💾➕ حفظ وإضافة جديد" Style="{StaticResource ModernButton}"
                        Margin="0,0,16,0" Click="BtnSaveAndNew_Click" MinWidth="160"/>
                <Button x:Name="BtnCancel" Content="❌ إلغاء" Style="{StaticResource SecondaryButton}"
                        Click="BtnCancel_Click" MinWidth="100"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
