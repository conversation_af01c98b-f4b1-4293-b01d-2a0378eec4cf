using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;

namespace ArabicDashboard.Services
{
    public class SettingsService
    {
        private readonly string _settingsFilePath;
        private Dictionary<string, object> _settings = new();

        public SettingsService()
        {
            _settingsFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "settings.json");
            LoadSettings();
        }

        private void LoadSettings()
        {
            try
            {
                if (File.Exists(_settingsFilePath))
                {
                    var json = File.ReadAllText(_settingsFilePath);
                    var deserializedSettings = JsonSerializer.Deserialize<Dictionary<string, object>>(json);
                    _settings = deserializedSettings ?? new Dictionary<string, object>();
                }
                else
                {
                    _settings = new Dictionary<string, object>();
                    SetDefaultSettings();
                    SaveSettings();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل الإعدادات: {ex.Message}");
                _settings = new Dictionary<string, object>();
                SetDefaultSettings();
            }
        }

        private void SetDefaultSettings()
        {
            // إعدادات النسخ الاحتياطي
            _settings["AutoBackupEnabled"] = true;
            _settings["BackupInterval"] = "يومياً";
            _settings["BackupPath"] = @"C:\ArabicDashboard\Backups";
            _settings["MaxBackupFiles"] = 10;

            // إعدادات الثيمات والمظهر المحسنة
            _settings["CurrentTheme"] = "Office2019Colorful";
            _settings["Theme"] = "Office2019Colorful";
            _settings["AccentColor"] = "#2563EB";
            _settings["FontFamily"] = "Segoe UI";
            _settings["FontSize"] = 14;
            _settings["Language"] = "Arabic";
            _settings["AnimationsEnabled"] = true;
            _settings["ThreeDEffectsEnabled"] = true;
            _settings["GradientBackgroundEnabled"] = true;
            _settings["ShadowEffectsEnabled"] = true;
            _settings["WindowOpacity"] = 1.0;

            // إعدادات الأداء المحسنة
            _settings["FastLoadingEnabled"] = true;
            _settings["CacheDataEnabled"] = true;
            _settings["AutoSaveEnabled"] = true;
            _settings["AutoSaveInterval"] = 5;
            _settings["PageSize"] = 100;
            _settings["EnableAnimations"] = true;
            _settings["CacheEnabled"] = true;
            _settings["MaxCacheSize"] = 100;
            _settings["AutoRefreshInterval"] = 30;

            // إعدادات التنبيهات
            _settings["NotificationsEnabled"] = true;
            _settings["PrayerNotificationsEnabled"] = true;
            _settings["SoundEnabled"] = true;
            _settings["ShowToastNotifications"] = true;
            _settings["NotificationDuration"] = 5000;

            // إعدادات قاعدة البيانات
            _settings["DatabaseAutoBackup"] = true;
            _settings["DatabaseBackupInterval"] = "يومياً";
            _settings["ConnectionTimeout"] = 30;

            // إعدادات الأمان
            _settings["RequirePasswordOnStartup"] = false;
            _settings["AutoLockAfterInactivity"] = false;
            _settings["InactivityTimeout"] = 15;
            _settings["EncryptBackups"] = false;

            // إعدادات التصدير والاستيراد
            _settings["DefaultExportFormat"] = "Excel";
            _settings["IncludeChartsInExport"] = true;
            _settings["ExportPath"] = @"C:\ArabicDashboard\Exports";

            // إعدادات التحديثات
            _settings["CheckForUpdatesAutomatically"] = true;
            _settings["UpdateChannel"] = "Stable";
            _settings["LastUpdateCheck"] = DateTime.Now.ToString("yyyy-MM-dd");

            // إعدادات السجلات
            _settings["EnableLogging"] = true;
            _settings["LogLevel"] = "معلومات";
            _settings["MaxLogFileSize"] = 10; // MB
            _settings["LogRetentionDays"] = 30;

            // إعدادات الأرشفة المحسنة
            _settings["AutoArchiveEnabled"] = true;
            _settings["ArchiveDay"] = 1;
            _settings["CompressArchiveEnabled"] = true;
            _settings["EncryptArchiveEnabled"] = false;
            _settings["AutoSave"] = true;
            _settings["MaxRecords"] = 1000;
            _settings["DatabaseOptimization"] = true;
            _settings["DataCleanupDays"] = 90;

            // إعدادات إضافية للأمان
            _settings["RememberLastSession"] = true;
            _settings["SendUsageData"] = false;

            // إعدادات إضافية للتنبيهات
            _settings["NotifyNewData"] = true;
            _settings["NotifyErrors"] = true;
            _settings["NotifyBackup"] = true;
            _settings["NotifyUpdates"] = true;
            _settings["NotifyReminders"] = true;
            _settings["NotifyWarnings"] = true;

            // إعدادات واجهة المستخدم المتقدمة
            _settings["ShowWelcomeScreen"] = true;
            _settings["ShowTips"] = true;
            _settings["AutoStartEnabled"] = false;
            _settings["MinimizeToTray"] = false;
            _settings["ShowInTaskbar"] = true;
            _settings["ConfirmExit"] = true;

            // إعدادات التقارير
            _settings["DefaultReportFormat"] = "PDF";
            _settings["IncludeLogosInReports"] = true;
            _settings["ReportLanguage"] = "Arabic";
            _settings["AutoGenerateReports"] = false;

            // إعدادات الطباعة
            _settings["DefaultPrinter"] = "";
            _settings["PrintOrientation"] = "Portrait";
            _settings["PrintMargins"] = "Normal";
            _settings["PrintQuality"] = "High";
        }

        public T GetSetting<T>(string key, T defaultValue = default)
        {
            try
            {
                if (_settings.ContainsKey(key))
                {
                    var value = _settings[key];
                    
                    if (value is JsonElement jsonElement)
                    {
                        var result = JsonSerializer.Deserialize<T>(jsonElement.GetRawText());
                        return result ?? defaultValue;
                    }
                    
                    if (value is T directValue)
                    {
                        return directValue;
                    }
                    
                    // محاولة التحويل
                    return (T)Convert.ChangeType(value, typeof(T));
                }
                
                return defaultValue;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في قراءة الإعداد {key}: {ex.Message}");
                return defaultValue;
            }
        }

        public void SetSetting<T>(string key, T value)
        {
            try
            {
                _settings[key] = value ?? throw new ArgumentNullException(nameof(value));
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في كتابة الإعداد {key}: {ex.Message}");
            }
        }

        public void SaveSettings()
        {
            try
            {
                var options = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                };

                var json = JsonSerializer.Serialize(_settings, options);
                
                // إنشاء المجلد إذا لم يكن موجوداً
                var directory = Path.GetDirectoryName(_settingsFilePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                File.WriteAllText(_settingsFilePath, json);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حفظ الإعدادات: {ex.Message}");
                throw;
            }
        }

        public void ResetToDefaults()
        {
            try
            {
                _settings.Clear();
                SetDefaultSettings();
                SaveSettings();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إعادة تعيين الإعدادات: {ex.Message}");
                throw;
            }
        }

        public Dictionary<string, object> GetAllSettings()
        {
            return new Dictionary<string, object>(_settings);
        }

        public bool HasSetting(string key)
        {
            return _settings.ContainsKey(key);
        }

        public void RemoveSetting(string key)
        {
            try
            {
                if (_settings.ContainsKey(key))
                {
                    _settings.Remove(key);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حذف الإعداد {key}: {ex.Message}");
            }
        }

        public void ImportSettings(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    throw new FileNotFoundException("ملف الإعدادات غير موجود");
                }

                var json = File.ReadAllText(filePath);
                var importedSettings = JsonSerializer.Deserialize<Dictionary<string, object>>(json);

                if (importedSettings != null)
                {
                    foreach (var setting in importedSettings)
                    {
                        _settings[setting.Key] = setting.Value;
                    }
                    SaveSettings();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في استيراد الإعدادات: {ex.Message}");
                throw;
            }
        }

        public void ExportSettings(string filePath)
        {
            try
            {
                var options = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                };

                var json = JsonSerializer.Serialize(_settings, options);
                
                // إنشاء المجلد إذا لم يكن موجوداً
                var directory = Path.GetDirectoryName(filePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                File.WriteAllText(filePath, json);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تصدير الإعدادات: {ex.Message}");
                throw;
            }
        }

        // دوال مساعدة للإعدادات الشائعة
        public string GetTheme() => GetSetting("Theme", "Light");
        public void SetTheme(string theme) => SetSetting("Theme", theme);

        public string GetAccentColor() => GetSetting("AccentColor", "#2563EB");
        public void SetAccentColor(string color) => SetSetting("AccentColor", color);

        public bool IsNotificationsEnabled() => GetSetting("NotificationsEnabled", true);
        public void SetNotificationsEnabled(bool enabled) => SetSetting("NotificationsEnabled", enabled);

        public bool IsAutoBackupEnabled() => GetSetting("AutoBackupEnabled", true);
        public void SetAutoBackupEnabled(bool enabled) => SetSetting("AutoBackupEnabled", enabled);

        public string GetBackupPath() => GetSetting("BackupPath", @"C:\ArabicDashboard\Backups");
        public void SetBackupPath(string path) => SetSetting("BackupPath", path);

        public int GetFontSize() => GetSetting("FontSize", 14);
        public void SetFontSize(int size) => SetSetting("FontSize", size);

        public string GetFontFamily() => GetSetting("FontFamily", "Segoe UI");
        public void SetFontFamily(string fontFamily) => SetSetting("FontFamily", fontFamily);
    }
}
