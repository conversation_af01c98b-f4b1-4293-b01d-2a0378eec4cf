# 🏢 نظام إدارة مكتب الخدمات العامة - الإصدار الاحترافي

## 📋 نظرة عامة

نظام شامل ومتطور لإدارة مكتب الخدمات العامة مع واجهة عربية احترافية وتصميم ثلاثي الأبعاد باستخدام مكتبة DevExpress.

---

## ✨ الميزات الرئيسية

### 🎯 إدارة الالتزامات
- ✅ **نموذج إدخال متقدم** مع جميع الحقول المطلوبة
- ✅ **تصميم ثلاثي الأبعاد** مع تأثيرات بصرية جذابة
- ✅ **نظام تنبيهات ذكي** للاستحقاقات
- ✅ **فلاتر متعددة** وبحث متقدم
- ✅ **إحصائيات فورية** ومحدثة تلقائياً

### 🧾 إدارة الفواتير والسندات
- ✅ **واجهة احترافية** مع بطاقات إحصائيات متطورة
- ✅ **جدول فواتير متقدم** مع تنسيق احترافي
- ✅ **قائمة سياقية شاملة** للإجراءات السريعة
- ✅ **تصدير وطباعة محسنة** للفواتير
- ✅ **إجراءات مجمعة** للفواتير المتعددة

### 👥 إدارة العمال
- ✅ **جدول عمال محسن** مع حساب الأيام المتبقية
- ✅ **ألوان تمييزية** للحالات والاستحقاقات
- ✅ **تنبيهات ذكية** عند اقتراب انتهاء الاشتراكات
- ✅ **إحصائيات مفصلة** للعمال النشطين

### 🏠 لوحة التحكم الرئيسية
- ✅ **ربط تفاعلي** مع قاعدة البيانات
- ✅ **إحصائيات حقيقية** ومحدثة فورياً
- ✅ **بطاقات ثلاثية الأبعاد** للمعلومات الهامة
- ✅ **تحديث تلقائي** عند التنقل بين النوافذ

---

## 🎨 نظام الثيمات المتقدم

### 🌈 28 ثيم احترافي متوفر:

#### مجموعة Office 2019:
- 🎨 Office 2019 ملون (افتراضي)
- ⚪ Office 2019 أبيض
- ⚫ Office 2019 أسود
- 🔆 Office 2019 تباين عالي

#### مجموعة Visual Studio:
- 🔵 Visual Studio أزرق
- 🌙 Visual Studio داكن
- ☀️ Visual Studio فاتح

#### ثيمات إبداعية:
- 🌈 Rainbow Theme
- 🏢 Corporate Blue
- 🌿 Nature Green
- 🔥 Fire Red
- 🌊 Ocean Blue
- 🌸 Sakura Pink
- وأكثر...

### ⚙️ إعدادات المظهر:
- **شفافية النوافذ** (70%-100%)
- **تأثيرات ثلاثية الأبعاد** قابلة للتحكم
- **انتقالات سلسة** بين الصفحات
- **حفظ تلقائي** للإعدادات المفضلة

---

## 🛠️ التقنيات المستخدمة

- **WPF (Windows Presentation Foundation)** - الإطار الأساسي
- **DevExpress WPF Controls** - مكونات واجهة المستخدم المتقدمة
- **Entity Framework Core** - إدارة قاعدة البيانات
- **SQLite** - قاعدة البيانات المحلية
- **C# .NET 6** - لغة البرمجة والإطار
- **MVVM Pattern** - نمط التصميم المعماري

---

## 📦 متطلبات التشغيل

### الحد الأدنى:
- **نظام التشغيل:** Windows 10 أو أحدث
- **الذاكرة:** 4 GB RAM
- **المساحة:** 500 MB مساحة فارغة
- **.NET Runtime:** .NET 6.0 أو أحدث

### الموصى به:
- **نظام التشغيل:** Windows 11
- **الذاكرة:** 8 GB RAM أو أكثر
- **المساحة:** 1 GB مساحة فارغة
- **الشاشة:** دقة 1920x1080 أو أعلى

---

## 🚀 التثبيت والتشغيل

### 1. تحميل المتطلبات:
```bash
# تثبيت .NET 6 Runtime
winget install Microsoft.DotNet.Runtime.6

# تثبيت DevExpress (إذا لم يكن مثبتاً)
# يتطلب ترخيص DevExpress صالح
```

### 2. تشغيل التطبيق:
```bash
# من مجلد المشروع
dotnet run

# أو تشغيل الملف التنفيذي مباشرة
ArabicDashboard.exe
```

### 3. الإعداد الأولي:
- سيتم إنشاء قاعدة البيانات تلقائياً عند التشغيل الأول
- سيتم إضافة بيانات تجريبية للاختبار
- يمكن تخصيص الإعدادات من قائمة الإعدادات

---

## 📚 دليل الاستخدام السريع

### 🎯 إدارة الالتزامات:
1. انقر على "الالتزامات" من القائمة الجانبية
2. استخدم "إضافة التزام جديد" لإنشاء التزام
3. املأ جميع الحقول المطلوبة (المميزة بـ *)
4. اختر إعدادات التنبيه المناسبة
5. احفظ الالتزام أو احفظ وأضف جديد

### 🧾 إدارة الفواتير:
1. انقر على "الفواتير والسندات" من القائمة
2. استخدم "إضافة فاتورة جديدة" لإنشاء فاتورة
3. استخدم القائمة السياقية (كليك يمين) للإجراءات السريعة
4. استخدم الفلاتر للبحث عن فواتير محددة

### 🎨 تغيير الثيم:
1. انقر على أيقونة الإعدادات
2. اختر "إعدادات المظهر"
3. اختر الثيم المفضل من القائمة
4. اضبط الشفافية والتأثيرات حسب الرغبة

---

## 🔧 الإعدادات المتقدمة

### ⚙️ إعدادات النظام:
- **الحفظ التلقائي:** كل 1-30 دقيقة
- **النسخ الاحتياطي:** يومي/أسبوعي/شهري
- **اللغة:** العربية (افتراضي) / الإنجليزية
- **التنبيهات:** صوتية/بصرية/كلاهما

### 🎨 إعدادات المظهر:
- **الثيم:** 28 ثيم متاح
- **الشفافية:** 70%-100%
- **التأثيرات ثلاثية الأبعاد:** تفعيل/إلغاء
- **الانتقالات:** سريعة/عادية/بطيئة

---

## 📊 الإحصائيات والتقارير

### 📈 الإحصائيات المتوفرة:
- **الإيرادات الشهرية** (محسوبة تلقائياً)
- **العمال النشطين** (مربوط بقاعدة البيانات)
- **الالتزامات المستحقة** (محدث فورياً)
- **إجمالي المعاملات** (من جميع الأقسام)

### 📋 التقارير:
- **تقارير الالتزامات** (حسب النوع/التاريخ/الحالة)
- **تقارير الفواتير** (مالية/إحصائية)
- **تقارير العمال** (الاشتراكات/الاستحقاقات)
- **تصدير متعدد الصيغ** (PDF/Excel/CSV)

---

## 🆘 الدعم والمساعدة

### 📞 طرق التواصل:
- **البريد الإلكتروني:** <EMAIL>
- **الهاتف:** +966-XX-XXX-XXXX
- **الموقع الإلكتروني:** www.arabicDashboard.com

### 🐛 الإبلاغ عن الأخطاء:
1. اذهب إلى قائمة "مساعدة" > "الإبلاغ عن خطأ"
2. اكتب وصفاً مفصلاً للمشكلة
3. أرفق لقطة شاشة إن أمكن
4. أرسل التقرير

---

## 📝 سجل التحديثات

### الإصدار 2.0.0 (ديسمبر 2024):
- ✅ تحسين شامل لصفحة الالتزامات
- ✅ تطوير صفحة الفواتير بتصميم احترافي
- ✅ إضافة 28 ثيم جديد
- ✅ تحسين الأداء والاستقرار
- ✅ ربط تفاعلي مع قاعدة البيانات

### الإصدار 1.5.0 (نوفمبر 2024):
- ✅ إضافة نظام الثيمات
- ✅ تحسين واجهة المستخدم
- ✅ إضافة نظام التنبيهات

---

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

---

## 🙏 شكر وتقدير

- **DevExpress** لمكتبة المكونات المتقدمة
- **Microsoft** لإطار عمل .NET و WPF
- **المجتمع العربي للمطورين** للدعم والمساهمات

---

**تم التطوير بواسطة:** فريق التطوير العربي  
**آخر تحديث:** ديسمبر 2024  
**الإصدار:** 2.0.0 Professional
