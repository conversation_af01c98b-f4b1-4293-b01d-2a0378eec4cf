<Window x:Class="ArabicDashboard.SettingsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="الإعدادات - لوحة التحكم العربية" 
        Height="600" Width="800"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        Background="#F5F6FA">

    <Window.Resources>
        <Style x:Key="ModernButton" TargetType="Button">
            <Setter Property="Background" Value="#3498DB"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="5"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#2980B9"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#21618C"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SettingsCard" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="10"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2" BlurRadius="10"/>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#2C3E50" Padding="20">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="⚙️" FontSize="24" Margin="0,0,10,0"/>
                <TextBlock Text="إعدادات التطبيق" 
                          FontSize="20" 
                          FontWeight="Bold" 
                          Foreground="White"/>
            </StackPanel>
        </Border>

        <!-- Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel Margin="20">
                
                <!-- إعدادات عامة -->
                <Border Style="{StaticResource SettingsCard}">
                    <StackPanel>
                        <TextBlock Text="🔧 الإعدادات العامة" 
                                  FontSize="16" 
                                  FontWeight="Bold" 
                                  Margin="0,0,0,15"/>
                        
                        <CheckBox Name="AutoStartCheckBox" 
                                 Content="تشغيل التطبيق تلقائياً مع بدء النظام" 
                                 Margin="0,5"/>
                        
                        <CheckBox Name="NotificationsCheckBox" 
                                 Content="تفعيل الإشعارات" 
                                 IsChecked="True"
                                 Margin="0,5"/>
                        
                        <CheckBox Name="PrayerNotificationsCheckBox" 
                                 Content="تفعيل تنبيهات أوقات الصلاة" 
                                 IsChecked="True"
                                 Margin="0,5"/>
                    </StackPanel>
                </Border>

                <!-- إعدادات قاعدة البيانات -->
                <Border Style="{StaticResource SettingsCard}">
                    <StackPanel>
                        <TextBlock Text="🗄️ إعدادات قاعدة البيانات" 
                                  FontSize="16" 
                                  FontWeight="Bold" 
                                  Margin="0,0,0,15"/>
                        
                        <StackPanel Orientation="Horizontal" Margin="0,5">
                            <TextBlock Text="مسار قاعدة البيانات:" 
                                      VerticalAlignment="Center" 
                                      Margin="0,0,10,0"/>
                            <TextBox Name="DatabasePathTextBox" 
                                    Width="300" 
                                    IsReadOnly="True"
                                    Background="#F8F9FA"/>
                            <Button Content="تصفح..." 
                                   Style="{StaticResource ModernButton}"
                                   Click="BrowseDatabasePath_Click"/>
                        </StackPanel>
                        
                        <StackPanel Orientation="Horizontal" Margin="0,10">
                            <Button Content="نسخ احتياطي" 
                                   Style="{StaticResource ModernButton}"
                                   Click="BackupDatabase_Click"/>
                            <Button Content="استعادة نسخة احتياطية" 
                                   Style="{StaticResource ModernButton}"
                                   Click="RestoreDatabase_Click"/>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- إعدادات الثيمات -->
                <Border Style="{StaticResource SettingsCard}">
                    <StackPanel>
                        <TextBlock Text="🎨 إعدادات الثيمات والمظهر"
                                  FontSize="16"
                                  FontWeight="Bold"
                                  Margin="0,0,0,15"/>

                        <StackPanel Orientation="Horizontal" Margin="0,5">
                            <TextBlock Text="اختر الثيم:"
                                      VerticalAlignment="Center"
                                      Margin="0,0,10,0"/>
                            <ComboBox Name="ThemeComboBox"
                                     Width="200"
                                     SelectedIndex="0"
                                     SelectionChanged="ThemeComboBox_SelectionChanged">
                                <ComboBoxItem Content="Office2019Colorful" Tag="Office2019Colorful"/>
                                <ComboBoxItem Content="Office2019White" Tag="Office2019White"/>
                                <ComboBoxItem Content="Office2019Black" Tag="Office2019Black"/>
                                <ComboBoxItem Content="Office2019HighContrast" Tag="Office2019HighContrast"/>
                                <ComboBoxItem Content="VS2019Blue" Tag="VS2019Blue"/>
                                <ComboBoxItem Content="VS2019Dark" Tag="VS2019Dark"/>
                                <ComboBoxItem Content="VS2019Light" Tag="VS2019Light"/>
                                <ComboBoxItem Content="Win10" Tag="Win10"/>
                                <ComboBoxItem Content="MetropolisLight" Tag="MetropolisLight"/>
                                <ComboBoxItem Content="MetropolisDark" Tag="MetropolisDark"/>
                            </ComboBox>
                        </StackPanel>

                        <CheckBox Name="AnimationsCheckBox"
                                 Content="تفعيل الانتقالات والمؤثرات البصرية"
                                 IsChecked="True"
                                 Margin="0,10,0,5"/>

                        <CheckBox Name="ThreeDEffectsCheckBox"
                                 Content="تفعيل التأثيرات ثلاثية الأبعاد"
                                 IsChecked="True"
                                 Margin="0,5"/>

                        <StackPanel Orientation="Horizontal" Margin="0,10">
                            <Button Content="🎨 معاينة الثيم"
                                   Style="{StaticResource ModernButton}"
                                   Background="#9B59B6"
                                   Click="PreviewTheme_Click"/>
                            <Button Content="🔄 إعادة تعيين الثيم"
                                   Style="{StaticResource ModernButton}"
                                   Background="#E67E22"
                                   Click="ResetTheme_Click"/>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- إعدادات الأرشفة -->
                <Border Style="{StaticResource SettingsCard}">
                    <StackPanel>
                        <TextBlock Text="📦 إعدادات الأرشفة التلقائية"
                                  FontSize="16"
                                  FontWeight="Bold"
                                  Margin="0,0,0,15"/>

                        <CheckBox Name="AutoArchiveCheckBox"
                                 Content="تفعيل الأرشفة التلقائية الشهرية"
                                 IsChecked="True"
                                 Margin="0,5"/>

                        <StackPanel Orientation="Horizontal" Margin="0,5">
                            <TextBlock Text="يوم الأرشفة من كل شهر:"
                                      VerticalAlignment="Center"
                                      Margin="0,0,10,0"/>
                            <ComboBox Name="ArchiveDayComboBox"
                                     Width="100"
                                     SelectedIndex="0">
                                <ComboBoxItem Content="1"/>
                                <ComboBoxItem Content="15"/>
                                <ComboBoxItem Content="30"/>
                            </ComboBox>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- معلومات التطبيق -->
                <Border Style="{StaticResource SettingsCard}">
                    <StackPanel>
                        <TextBlock Text="ℹ️ معلومات التطبيق" 
                                  FontSize="16" 
                                  FontWeight="Bold" 
                                  Margin="0,0,0,15"/>
                        
                        <TextBlock Text="اسم التطبيق: لوحة التحكم العربية" Margin="0,2"/>
                        <TextBlock Text="الإصدار: 1.0.0" Margin="0,2"/>
                        <TextBlock Text="تاريخ الإصدار: 2024" Margin="0,2"/>
                        <TextBlock Text="المطور: فريق التطوير العربي" Margin="0,2"/>
                    </StackPanel>
                </Border>

            </StackPanel>
        </ScrollViewer>

        <!-- Footer Buttons -->
        <Border Grid.Row="2" Background="#ECF0F1" Padding="20">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Content="💾 حفظ الإعدادات" 
                       Style="{StaticResource ModernButton}"
                       Background="#27AE60"
                       Click="SaveSettings_Click"/>
                <Button Content="🔄 إعادة تعيين" 
                       Style="{StaticResource ModernButton}"
                       Background="#E74C3C"
                       Click="ResetSettings_Click"/>
                <Button Content="❌ إلغاء" 
                       Style="{StaticResource ModernButton}"
                       Background="#95A5A6"
                       Click="Cancel_Click"/>
            </StackPanel>
        </Border>

    </Grid>
</Window>
