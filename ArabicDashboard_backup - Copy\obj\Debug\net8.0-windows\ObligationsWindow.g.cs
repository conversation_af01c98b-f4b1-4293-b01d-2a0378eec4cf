﻿#pragma checksum "..\..\..\ObligationsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "D5A5C4999F20D6AEA003FF6C68B719A3CE3CEE2E"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using DevExpress.Core;
using DevExpress.Xpf.Core;
using DevExpress.Xpf.Core.ConditionalFormatting;
using DevExpress.Xpf.Core.DataSources;
using DevExpress.Xpf.Core.Serialization;
using DevExpress.Xpf.Core.ServerMode;
using DevExpress.Xpf.DXBinding;
using DevExpress.Xpf.Data;
using DevExpress.Xpf.Editors;
using DevExpress.Xpf.Editors.DataPager;
using DevExpress.Xpf.Editors.DateNavigator;
using DevExpress.Xpf.Editors.ExpressionEditor;
using DevExpress.Xpf.Editors.Filtering;
using DevExpress.Xpf.Editors.Flyout;
using DevExpress.Xpf.Editors.Popups;
using DevExpress.Xpf.Editors.Popups.Calendar;
using DevExpress.Xpf.Editors.RangeControl;
using DevExpress.Xpf.Editors.Settings;
using DevExpress.Xpf.Editors.Settings.Extension;
using DevExpress.Xpf.Editors.Validation;
using DevExpress.Xpf.Grid;
using DevExpress.Xpf.Grid.ConditionalFormatting;
using DevExpress.Xpf.Grid.LookUp;
using DevExpress.Xpf.Grid.TreeList;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace ArabicDashboard {
    
    
    /// <summary>
    /// ObligationsWindow
    /// </summary>
    public partial class ObligationsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 84 "..\..\..\ObligationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnAddObligation;
        
        #line default
        #line hidden
        
        
        #line 86 "..\..\..\ObligationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnRefresh;
        
        #line default
        #line hidden
        
        
        #line 88 "..\..\..\ObligationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnImport;
        
        #line default
        #line hidden
        
        
        #line 90 "..\..\..\ObligationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnExport;
        
        #line default
        #line hidden
        
        
        #line 92 "..\..\..\ObligationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnPrint;
        
        #line default
        #line hidden
        
        
        #line 123 "..\..\..\ObligationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtSearch;
        
        #line default
        #line hidden
        
        
        #line 164 "..\..\..\ObligationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CmbStatusFilter;
        
        #line default
        #line hidden
        
        
        #line 176 "..\..\..\ObligationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CmbTypeFilter;
        
        #line default
        #line hidden
        
        
        #line 190 "..\..\..\ObligationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnSearch;
        
        #line default
        #line hidden
        
        
        #line 206 "..\..\..\ObligationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker DateFrom;
        
        #line default
        #line hidden
        
        
        #line 210 "..\..\..\ObligationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker DateTo;
        
        #line default
        #line hidden
        
        
        #line 213 "..\..\..\ObligationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnClearFilters;
        
        #line default
        #line hidden
        
        
        #line 217 "..\..\..\ObligationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnAdvancedSearch;
        
        #line default
        #line hidden
        
        
        #line 225 "..\..\..\ObligationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Grid.GridControl ObligationsGrid;
        
        #line default
        #line hidden
        
        
        #line 288 "..\..\..\ObligationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTotalObligations;
        
        #line default
        #line hidden
        
        
        #line 290 "..\..\..\ObligationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtActiveObligations;
        
        #line default
        #line hidden
        
        
        #line 292 "..\..\..\ObligationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtOverdueObligations;
        
        #line default
        #line hidden
        
        
        #line 295 "..\..\..\ObligationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtLastUpdate;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/ArabicDashboard;component/obligationswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\ObligationsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.BtnAddObligation = ((System.Windows.Controls.Button)(target));
            
            #line 85 "..\..\..\ObligationsWindow.xaml"
            this.BtnAddObligation.Click += new System.Windows.RoutedEventHandler(this.BtnAddObligation_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.BtnRefresh = ((System.Windows.Controls.Button)(target));
            
            #line 87 "..\..\..\ObligationsWindow.xaml"
            this.BtnRefresh.Click += new System.Windows.RoutedEventHandler(this.BtnRefresh_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.BtnImport = ((System.Windows.Controls.Button)(target));
            
            #line 89 "..\..\..\ObligationsWindow.xaml"
            this.BtnImport.Click += new System.Windows.RoutedEventHandler(this.BtnImport_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.BtnExport = ((System.Windows.Controls.Button)(target));
            
            #line 91 "..\..\..\ObligationsWindow.xaml"
            this.BtnExport.Click += new System.Windows.RoutedEventHandler(this.BtnExport_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.BtnPrint = ((System.Windows.Controls.Button)(target));
            
            #line 93 "..\..\..\ObligationsWindow.xaml"
            this.BtnPrint.Click += new System.Windows.RoutedEventHandler(this.BtnPrint_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.TxtSearch = ((System.Windows.Controls.TextBox)(target));
            return;
            case 7:
            this.CmbStatusFilter = ((System.Windows.Controls.ComboBox)(target));
            
            #line 168 "..\..\..\ObligationsWindow.xaml"
            this.CmbStatusFilter.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.CmbStatusFilter_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 8:
            this.CmbTypeFilter = ((System.Windows.Controls.ComboBox)(target));
            
            #line 180 "..\..\..\ObligationsWindow.xaml"
            this.CmbTypeFilter.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.CmbTypeFilter_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 9:
            this.BtnSearch = ((System.Windows.Controls.Button)(target));
            
            #line 191 "..\..\..\ObligationsWindow.xaml"
            this.BtnSearch.Click += new System.Windows.RoutedEventHandler(this.BtnSearch_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.DateFrom = ((System.Windows.Controls.DatePicker)(target));
            
            #line 207 "..\..\..\ObligationsWindow.xaml"
            this.DateFrom.SelectedDateChanged += new System.EventHandler<System.Windows.Controls.SelectionChangedEventArgs>(this.DateFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 11:
            this.DateTo = ((System.Windows.Controls.DatePicker)(target));
            
            #line 211 "..\..\..\ObligationsWindow.xaml"
            this.DateTo.SelectedDateChanged += new System.EventHandler<System.Windows.Controls.SelectionChangedEventArgs>(this.DateFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 12:
            this.BtnClearFilters = ((System.Windows.Controls.Button)(target));
            
            #line 215 "..\..\..\ObligationsWindow.xaml"
            this.BtnClearFilters.Click += new System.Windows.RoutedEventHandler(this.BtnClearFilters_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.BtnAdvancedSearch = ((System.Windows.Controls.Button)(target));
            
            #line 219 "..\..\..\ObligationsWindow.xaml"
            this.BtnAdvancedSearch.Click += new System.Windows.RoutedEventHandler(this.BtnAdvancedSearch_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.ObligationsGrid = ((DevExpress.Xpf.Grid.GridControl)(target));
            return;
            case 15:
            
            #line 260 "..\..\..\ObligationsWindow.xaml"
            ((DevExpress.Xpf.Grid.TableView)(target)).RowDoubleClick += new DevExpress.Xpf.Grid.RowDoubleClickEventHandler(this.TableView_RowDoubleClick);
            
            #line default
            #line hidden
            return;
            case 16:
            
            #line 265 "..\..\..\ObligationsWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.EditObligation_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            
            #line 266 "..\..\..\ObligationsWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewObligation_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            
            #line 268 "..\..\..\ObligationsWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.MarkAsCompleted_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            
            #line 269 "..\..\..\ObligationsWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.ReactivateObligation_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            
            #line 271 "..\..\..\ObligationsWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteObligation_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            this.TxtTotalObligations = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 22:
            this.TxtActiveObligations = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 23:
            this.TxtOverdueObligations = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 24:
            this.TxtLastUpdate = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

