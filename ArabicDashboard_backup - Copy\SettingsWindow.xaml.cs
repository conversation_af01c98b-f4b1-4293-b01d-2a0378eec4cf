using System;
using System.IO;
using System.Windows;
using System.Windows.Controls;
using Microsoft.Win32;
using ArabicDashboard.Services;
using DevExpress.Xpf.Core;
using MessageBox = System.Windows.MessageBox;
using OpenFileDialog = Microsoft.Win32.OpenFileDialog;
using SaveFileDialog = Microsoft.Win32.SaveFileDialog;
using Application = System.Windows.Application;

namespace ArabicDashboard
{
    public partial class SettingsWindow : Window
    {
        private readonly SettingsService _settingsService;

        public SettingsWindow()
        {
            InitializeComponent();
            _settingsService = new SettingsService();
            LoadCurrentSettings();
        }

        private void LoadCurrentSettings()
        {
            try
            {
                // تحميل الإعدادات الحالية
                AutoStartCheckBox.IsChecked = _settingsService.GetSetting("AutoStart", false);
                NotificationsCheckBox.IsChecked = _settingsService.GetSetting("NotificationsEnabled", true);
                PrayerNotificationsCheckBox.IsChecked = _settingsService.GetSetting("PrayerNotificationsEnabled", true);
                AutoArchiveCheckBox.IsChecked = _settingsService.GetSetting("AutoArchiveEnabled", true);

                // تحميل إعدادات الثيمات
                var currentTheme = _settingsService.GetSetting("CurrentTheme", "Office2019Colorful");
                SelectThemeInComboBox(currentTheme);
                AnimationsCheckBox.IsChecked = _settingsService.GetSetting("AnimationsEnabled", true);
                ThreeDEffectsCheckBox.IsChecked = _settingsService.GetSetting("ThreeDEffectsEnabled", true);

                // عرض مسار قاعدة البيانات
                DatabasePathTextBox.Text = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "ArabicDashboard.db");

                // تحديد يوم الأرشفة
                var archiveDay = _settingsService.GetSetting("ArchiveDay", 1);
                ArchiveDayComboBox.SelectedIndex = archiveDay switch
                {
                    1 => 0,
                    15 => 1,
                    30 => 2,
                    _ => 0
                };
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الإعدادات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SaveSettings_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // حفظ الإعدادات الجديدة
                _settingsService.SetSetting("AutoStart", AutoStartCheckBox.IsChecked ?? false);
                _settingsService.SetSetting("NotificationsEnabled", NotificationsCheckBox.IsChecked ?? true);
                _settingsService.SetSetting("PrayerNotificationsEnabled", PrayerNotificationsCheckBox.IsChecked ?? true);
                _settingsService.SetSetting("AutoArchiveEnabled", AutoArchiveCheckBox.IsChecked ?? true);

                // حفظ إعدادات الثيمات
                var selectedTheme = GetSelectedTheme();
                _settingsService.SetSetting("CurrentTheme", selectedTheme);
                _settingsService.SetSetting("AnimationsEnabled", AnimationsCheckBox.IsChecked ?? true);
                _settingsService.SetSetting("ThreeDEffectsEnabled", ThreeDEffectsCheckBox.IsChecked ?? true);

                var archiveDay = ArchiveDayComboBox.SelectedIndex switch
                {
                    0 => 1,
                    1 => 15,
                    2 => 30,
                    _ => 1
                };
                _settingsService.SetSetting("ArchiveDay", archiveDay);

                _settingsService.SaveSettings();

                MessageBox.Show("تم حفظ الإعدادات بنجاح! ✅", "نجح الحفظ",
                    MessageBoxButton.OK, MessageBoxImage.Information);

                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الإعدادات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ResetSettings_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟", 
                "تأكيد إعادة التعيين", MessageBoxButton.YesNo, MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    _settingsService.ResetToDefaults();
                    LoadCurrentSettings();

                    MessageBox.Show("تم إعادة تعيين الإعدادات بنجاح! 🔄", "تم إعادة التعيين",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في إعادة تعيين الإعدادات: {ex.Message}", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        private void BrowseDatabasePath_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new OpenFileDialog
            {
                Title = "اختر ملف قاعدة البيانات",
                Filter = "ملفات قاعدة البيانات (*.db)|*.db|جميع الملفات (*.*)|*.*",
                InitialDirectory = AppDomain.CurrentDomain.BaseDirectory
            };

            if (dialog.ShowDialog() == true)
            {
                DatabasePathTextBox.Text = dialog.FileName;
            }
        }

        private void BackupDatabase_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var dialog = new SaveFileDialog
                {
                    Title = "حفظ نسخة احتياطية من قاعدة البيانات",
                    Filter = "ملفات قاعدة البيانات (*.db)|*.db",
                    FileName = $"ArabicDashboard_Backup_{DateTime.Now:yyyyMMdd_HHmmss}.db"
                };

                if (dialog.ShowDialog() == true)
                {
                    var sourcePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "ArabicDashboard.db");
                    
                    if (File.Exists(sourcePath))
                    {
                        File.Copy(sourcePath, dialog.FileName, true);
                        MessageBox.Show("تم إنشاء النسخة الاحتياطية بنجاح! 💾", "نجح النسخ الاحتياطي", 
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show("لم يتم العثور على ملف قاعدة البيانات!", "خطأ", 
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء النسخة الاحتياطية: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void RestoreDatabase_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("تحذير: سيتم استبدال قاعدة البيانات الحالية بالنسخة الاحتياطية المختارة.\nهل أنت متأكد؟", 
                "تأكيد الاستعادة", MessageBoxButton.YesNo, MessageBoxImage.Warning);
            
            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    var dialog = new OpenFileDialog
                    {
                        Title = "اختر النسخة الاحتياطية لاستعادتها",
                        Filter = "ملفات قاعدة البيانات (*.db)|*.db|جميع الملفات (*.*)|*.*"
                    };

                    if (dialog.ShowDialog() == true)
                    {
                        var targetPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "ArabicDashboard.db");
                        File.Copy(dialog.FileName, targetPath, true);
                        
                        MessageBox.Show("تم استعادة النسخة الاحتياطية بنجاح! 🔄\nسيتم إعادة تشغيل التطبيق.", 
                            "نجحت الاستعادة", MessageBoxButton.OK, MessageBoxImage.Information);
                        
                        // إعادة تشغيل التطبيق
                        System.Diagnostics.Process.Start(System.Diagnostics.Process.GetCurrentProcess().MainModule.FileName);
                        Application.Current.Shutdown();
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في استعادة النسخة الاحتياطية: {ex.Message}", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        // ===== معالجات الثيمات =====

        private void ThemeComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                if (ThemeComboBox.SelectedItem is ComboBoxItem selectedItem)
                {
                    var themeName = selectedItem.Tag?.ToString();
                    if (!string.IsNullOrEmpty(themeName))
                    {
                        ApplyTheme(themeName);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تغيير الثيم: {ex.Message}");
            }
        }

        private void PreviewTheme_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var selectedTheme = GetSelectedTheme();
                ApplyTheme(selectedTheme);

                MessageBox.Show($"تم تطبيق الثيم: {selectedTheme}\n\nيمكنك رؤية التغييرات في النوافذ المفتوحة.",
                    "معاينة الثيم", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في معاينة الثيم: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ResetTheme_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                ThemeComboBox.SelectedIndex = 0; // Office2019Colorful
                AnimationsCheckBox.IsChecked = true;
                ThreeDEffectsCheckBox.IsChecked = true;

                ApplyTheme("Office2019Colorful");

                MessageBox.Show("تم إعادة تعيين الثيم إلى الافتراضي بنجاح!", "إعادة تعيين الثيم",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إعادة تعيين الثيم: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ApplyTheme(string themeName)
        {
            try
            {
                // تطبيق الثيم على التطبيق
                ThemeManager.SetThemeName(Application.Current.MainWindow, themeName);

                // تطبيق الثيم على النافذة الحالية
                ThemeManager.SetThemeName(this, themeName);

                System.Diagnostics.Debug.WriteLine($"تم تطبيق الثيم: {themeName}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق الثيم: {ex.Message}");
            }
        }

        private string GetSelectedTheme()
        {
            if (ThemeComboBox.SelectedItem is ComboBoxItem selectedItem)
            {
                return selectedItem.Tag?.ToString() ?? "Office2019Colorful";
            }
            return "Office2019Colorful";
        }

        private void SelectThemeInComboBox(string themeName)
        {
            for (int i = 0; i < ThemeComboBox.Items.Count; i++)
            {
                if (ThemeComboBox.Items[i] is ComboBoxItem item &&
                    item.Tag?.ToString() == themeName)
                {
                    ThemeComboBox.SelectedIndex = i;
                    break;
                }
            }
        }
    }
}
