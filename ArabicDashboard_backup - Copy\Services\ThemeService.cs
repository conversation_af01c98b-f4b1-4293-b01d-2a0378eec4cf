using System;
using System.Windows;
using DevExpress.Xpf.Core;
using Application = System.Windows.Application;

namespace ArabicDashboard.Services
{
    /// <summary>
    /// خدمة إدارة الثيمات والمظهر
    /// </summary>
    public class ThemeService
    {
        private readonly SettingsService _settingsService;

        public ThemeService()
        {
            _settingsService = new SettingsService();
        }

        /// <summary>
        /// تطبيق الثيم المحفوظ في الإعدادات
        /// </summary>
        public void ApplySavedTheme()
        {
            try
            {
                var savedTheme = _settingsService.GetSetting("CurrentTheme", "Office2019Colorful");
                ApplyTheme(savedTheme);
                
                System.Diagnostics.Debug.WriteLine($"تم تطبيق الثيم المحفوظ: {savedTheme}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق الثيم المحفوظ: {ex.Message}");
                // تطبيق الثيم الافتراضي في حالة الخطأ
                ApplyTheme("Office2019Colorful");
            }
        }

        /// <summary>
        /// تطبيق ثيم محدد
        /// </summary>
        /// <param name="themeName">اسم الثيم</param>
        public void ApplyTheme(string themeName)
        {
            try
            {
                // تطبيق الثيم على النافذة الرئيسية
                if (Application.Current.MainWindow != null)
                {
                    ThemeManager.SetThemeName(Application.Current.MainWindow, themeName);
                }

                // تطبيق الثيم على جميع النوافذ المفتوحة
                foreach (Window window in Application.Current.Windows)
                {
                    ThemeManager.SetThemeName(window, themeName);
                }

                // حفظ الثيم في الإعدادات
                _settingsService.SetSetting("CurrentTheme", themeName);
                _settingsService.SaveSettings();

                System.Diagnostics.Debug.WriteLine($"تم تطبيق الثيم بنجاح: {themeName}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق الثيم {themeName}: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على الثيم الحالي
        /// </summary>
        /// <returns>اسم الثيم الحالي</returns>
        public string GetCurrentTheme()
        {
            return _settingsService.GetSetting("CurrentTheme", "Office2019Colorful");
        }

        /// <summary>
        /// الحصول على قائمة الثيمات المتاحة
        /// </summary>
        /// <returns>مصفوفة أسماء الثيمات</returns>
        public string[] GetAvailableThemes()
        {
            return new string[]
            {
                // ثيمات Office 2019
                "Office2019Colorful",
                "Office2019White",
                "Office2019Black",
                "Office2019HighContrast",

                // ثيمات Visual Studio 2019
                "VS2019Blue",
                "VS2019Dark",
                "VS2019Light",

                // ثيمات Windows
                "Win10",
                "Win11",

                // ثيمات Metropolis
                "MetropolisLight",
                "MetropolisDark",

                // ثيمات إضافية
                "MaterialDesign",
                "FluentDesign",
                "Rainbow",
                "CorporateBlue",
                "NatureGreen",
                "FireRed",
                "OceanBlue",
                "SakuraPink"
            };
        }

        /// <summary>
        /// الحصول على أسماء الثيمات باللغة العربية
        /// </summary>
        /// <returns>مصفوفة أسماء الثيمات بالعربية</returns>
        public string[] GetThemeDisplayNames()
        {
            return new string[]
            {
                // ثيمات Office 2019
                "🎨 Office 2019 ملون",
                "⚪ Office 2019 أبيض",
                "⚫ Office 2019 أسود",
                "🔆 Office 2019 تباين عالي",

                // ثيمات Visual Studio 2019
                "🔵 Visual Studio أزرق",
                "🌙 Visual Studio داكن",
                "☀️ Visual Studio فاتح",

                // ثيمات Windows
                "🪟 Windows 10",
                "🌆 Windows 11",

                // ثيمات Metropolis
                "🌅 Metropolis فاتح",
                "🌃 Metropolis داكن",

                // ثيمات إضافية
                "💎 Material Design",
                "🎯 Fluent Design",
                "🌈 Rainbow Theme",
                "🏢 Corporate Blue",
                "🌿 Nature Green",
                "🔥 Fire Red",
                "🌊 Ocean Blue",
                "🌸 Sakura Pink"
            };
        }

        /// <summary>
        /// تفعيل أو إلغاء تفعيل الانتقالات والمؤثرات
        /// </summary>
        /// <param name="enabled">تفعيل الانتقالات</param>
        public void SetAnimationsEnabled(bool enabled)
        {
            try
            {
                _settingsService.SetSetting("AnimationsEnabled", enabled);
                _settingsService.SaveSettings();
                
                // يمكن إضافة منطق إضافي هنا لتطبيق/إلغاء المؤثرات
                System.Diagnostics.Debug.WriteLine($"تم تعيين الانتقالات: {enabled}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تعيين الانتقالات: {ex.Message}");
            }
        }

        /// <summary>
        /// تفعيل أو إلغاء تفعيل التأثيرات ثلاثية الأبعاد
        /// </summary>
        /// <param name="enabled">تفعيل التأثيرات ثلاثية الأبعاد</param>
        public void SetThreeDEffectsEnabled(bool enabled)
        {
            try
            {
                _settingsService.SetSetting("ThreeDEffectsEnabled", enabled);
                _settingsService.SaveSettings();
                
                // يمكن إضافة منطق إضافي هنا لتطبيق/إلغاء التأثيرات ثلاثية الأبعاد
                System.Diagnostics.Debug.WriteLine($"تم تعيين التأثيرات ثلاثية الأبعاد: {enabled}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تعيين التأثيرات ثلاثية الأبعاد: {ex.Message}");
            }
        }

        /// <summary>
        /// التحقق من تفعيل الانتقالات
        /// </summary>
        /// <returns>حالة تفعيل الانتقالات</returns>
        public bool IsAnimationsEnabled()
        {
            return _settingsService.GetSetting("AnimationsEnabled", true);
        }

        /// <summary>
        /// التحقق من تفعيل التأثيرات ثلاثية الأبعاد
        /// </summary>
        /// <returns>حالة تفعيل التأثيرات ثلاثية الأبعاد</returns>
        public bool IsThreeDEffectsEnabled()
        {
            return _settingsService.GetSetting("ThreeDEffectsEnabled", true);
        }

        /// <summary>
        /// إعادة تعيين الثيم إلى الافتراضي
        /// </summary>
        public void ResetToDefaultTheme()
        {
            try
            {
                ApplyTheme("Office2019Colorful");
                SetAnimationsEnabled(true);
                SetThreeDEffectsEnabled(true);
                
                System.Diagnostics.Debug.WriteLine("تم إعادة تعيين الثيم إلى الافتراضي");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إعادة تعيين الثيم: {ex.Message}");
            }
        }

        /// <summary>
        /// تطبيق ثيم على نافذة محددة
        /// </summary>
        /// <param name="window">النافذة المراد تطبيق الثيم عليها</param>
        /// <param name="themeName">اسم الثيم</param>
        public void ApplyThemeToWindow(Window window, string themeName = null)
        {
            try
            {
                var theme = themeName ?? GetCurrentTheme();
                ThemeManager.SetThemeName(window, theme);
                
                System.Diagnostics.Debug.WriteLine($"تم تطبيق الثيم {theme} على النافذة {window.GetType().Name}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق الثيم على النافذة: {ex.Message}");
            }
        }
    }
}
