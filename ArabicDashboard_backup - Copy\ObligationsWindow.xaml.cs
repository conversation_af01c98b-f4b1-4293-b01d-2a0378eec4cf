using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using ArabicDashboard.Models;
using ArabicDashboard.Services;
using DevExpress.Xpf.Core;
using DevExpress.Xpf.Grid;

namespace ArabicDashboard
{
    public partial class ObligationsWindow : Window, INotifyPropertyChanged
    {
        private readonly DatabaseService _databaseService;
        private List<Obligation> _obligations = new List<Obligation>();
        private string _searchText = "";

        public event PropertyChangedEventHandler? PropertyChanged;

        public string SearchText
        {
            get => _searchText;
            set
            {
                _searchText = value;
                OnPropertyChanged(nameof(SearchText));
                FilterObligations();
            }
        }

        public ObligationsWindow()
        {
            InitializeComponent();
            _databaseService = new DatabaseService();
            DataContext = this;
            Loaded += ObligationsWindow_Loaded;
        }

        private async void ObligationsWindow_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadObligations();
        }

        private async Task LoadObligations()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("بدء تحميل الالتزامات...");
                
                _obligations = await _databaseService.GetObligationsAsync();
                ObligationsGrid.ItemsSource = _obligations;
                
                UpdateStatistics();
                TxtLastUpdate.Text = $"آخر تحديث: {DateTime.Now:HH:mm:ss}";
                
                System.Diagnostics.Debug.WriteLine($"تم تحميل {_obligations.Count} التزام");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل الالتزامات: {ex.Message}");
                DXMessageBox.Show($"خطأ في تحميل الالتزامات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateStatistics()
        {
            var total = _obligations.Count;
            var active = _obligations.Count(o => o.Status == "نشط");
            var overdue = _obligations.Count(o => o.IsOverdue);

            TxtTotalObligations.Text = total.ToString();
            TxtActiveObligations.Text = active.ToString();
            TxtOverdueObligations.Text = overdue.ToString();
        }

        private void FilterObligations()
        {
            if (_obligations == null) return;

            var filtered = _obligations.AsEnumerable();

            // فلترة بالنص
            if (!string.IsNullOrWhiteSpace(SearchText))
            {
                filtered = filtered.Where(o =>
                    o.Name.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                    o.Type.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                    o.Notes.Contains(SearchText, StringComparison.OrdinalIgnoreCase));
            }

            // فلترة بالحالة
            if (CmbStatusFilter?.SelectedItem is ComboBoxItem selectedStatusItem)
            {
                var status = selectedStatusItem.Content.ToString();
                if (status != "جميع الحالات")
                {
                    filtered = status switch
                    {
                        "نشط" => filtered.Where(o => o.Status == "نشط" && !o.IsOverdue && !o.IsNearDue),
                        "مكتمل" => filtered.Where(o => o.Status == "مكتمل"),
                        "متأخر" => filtered.Where(o => o.IsOverdue),
                        "قريب الاستحقاق" => filtered.Where(o => o.IsNearDue),
                        _ => filtered
                    };
                }
            }

            // فلترة بنوع الالتزام
            if (CmbTypeFilter?.SelectedItem is ComboBoxItem selectedTypeItem)
            {
                var type = selectedTypeItem.Content.ToString();
                if (type != "جميع الأنواع")
                {
                    filtered = filtered.Where(o => o.Type.Contains(type, StringComparison.OrdinalIgnoreCase));
                }
            }

            // فلترة بالتاريخ
            if (DateFrom?.SelectedDate.HasValue == true)
            {
                filtered = filtered.Where(o => o.DueDate >= DateFrom.SelectedDate.Value);
            }

            if (DateTo?.SelectedDate.HasValue == true)
            {
                filtered = filtered.Where(o => o.DueDate <= DateTo.SelectedDate.Value);
            }

            var result = filtered.ToList();
            ObligationsGrid.ItemsSource = result;

            // تحديث الإحصائيات بناءً على النتائج المفلترة
            UpdateFilteredStatistics(result);
        }

        private void UpdateFilteredStatistics(List<Obligation> filteredObligations)
        {
            var total = filteredObligations.Count;
            var active = filteredObligations.Count(o => o.Status == "نشط");
            var overdue = filteredObligations.Count(o => o.IsOverdue);

            TxtTotalObligations.Text = total.ToString();
            TxtActiveObligations.Text = active.ToString();
            TxtOverdueObligations.Text = overdue.ToString();
        }

        private async void BtnAddObligation_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var addWindow = new ObligationFormWindow();
                addWindow.ObligationSaved += async (s, args) => await LoadObligations();
                addWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في فتح نافذة إضافة الالتزام: {ex.Message}");
                DXMessageBox.Show("خطأ في فتح نافذة إضافة الالتزام", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void BtnRefresh_Click(object sender, RoutedEventArgs e)
        {
            await LoadObligations();
        }

        private void BtnSearch_Click(object sender, RoutedEventArgs e)
        {
            FilterObligations();
        }

        private void CmbStatusFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            FilterObligations();
        }

        private void CmbTypeFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            FilterObligations();
        }

        private void DateFilter_Changed(object sender, SelectionChangedEventArgs e)
        {
            FilterObligations();
        }

        private void BtnClearFilters_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // مسح جميع الفلاتر
                TxtSearch.Text = "";
                CmbStatusFilter.SelectedIndex = 0;
                CmbTypeFilter.SelectedIndex = 0;
                DateFrom.SelectedDate = null;
                DateTo.SelectedDate = null;

                // إعادة تحميل جميع البيانات
                FilterObligations();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في مسح الفلاتر: {ex.Message}");
            }
        }

        private void BtnAdvancedSearch_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // يمكن إضافة نافذة بحث متقدم هنا
                DXMessageBox.Show("ميزة البحث المتقدم قيد التطوير", "قريباً",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في البحث المتقدم: {ex.Message}");
            }
        }

        private void BtnImport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var dialog = new Microsoft.Win32.OpenFileDialog
                {
                    Title = "استيراد الالتزامات",
                    Filter = "Excel Files|*.xlsx;*.xls|CSV Files|*.csv|جميع الملفات|*.*"
                };

                if (dialog.ShowDialog() == true)
                {
                    // يمكن إضافة منطق الاستيراد هنا
                    DXMessageBox.Show("ميزة الاستيراد قيد التطوير", "قريباً",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في الاستيراد: {ex.Message}");
                DXMessageBox.Show("خطأ في استيراد البيانات", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnPrint_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // طباعة التقرير
                var printDialog = new System.Windows.Controls.PrintDialog();
                if (printDialog.ShowDialog() == true)
                {
                    // يمكن إضافة منطق الطباعة هنا
                    DXMessageBox.Show("ميزة الطباعة قيد التطوير", "قريباً",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في الطباعة: {ex.Message}");
                DXMessageBox.Show("خطأ في طباعة التقرير", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnExport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // تصدير البيانات إلى Excel
                var saveDialog = new Microsoft.Win32.SaveFileDialog
                {
                    Filter = "Excel Files|*.xlsx",
                    FileName = $"الالتزامات_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx"
                };

                if (saveDialog.ShowDialog() == true)
                {
                    ObligationsGrid.View.ExportToXlsx(saveDialog.FileName);
                    DXMessageBox.Show("تم تصدير البيانات بنجاح", "نجح", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تصدير البيانات: {ex.Message}");
                DXMessageBox.Show("خطأ في تصدير البيانات", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void TableView_RowDoubleClick(object sender, RowDoubleClickEventArgs e)
        {
            await EditSelectedObligation();
        }

        private async void EditObligation_Click(object sender, RoutedEventArgs e)
        {
            await EditSelectedObligation();
        }

        private async Task EditSelectedObligation()
        {
            try
            {
                if (ObligationsGrid.SelectedItem is Obligation selectedObligation)
                {
                    var editWindow = new ObligationFormWindow(selectedObligation);
                    editWindow.ObligationSaved += async (s, args) => await LoadObligations();
                    editWindow.ShowDialog();
                }
                else
                {
                    DXMessageBox.Show("يرجى اختيار التزام للتعديل", "تنبيه", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تعديل الالتزام: {ex.Message}");
                DXMessageBox.Show("خطأ في تعديل الالتزام", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ViewObligation_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (ObligationsGrid.SelectedItem is Obligation selectedObligation)
                {
                    var viewWindow = new ObligationFormWindow(selectedObligation, true);
                    viewWindow.ShowDialog();
                }
                else
                {
                    DXMessageBox.Show("يرجى اختيار التزام للعرض", "تنبيه", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في عرض الالتزام: {ex.Message}");
                DXMessageBox.Show("خطأ في عرض الالتزام", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void MarkAsCompleted_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (ObligationsGrid.SelectedItem is Obligation selectedObligation)
                {
                    var result = DXMessageBox.Show("هل أنت متأكد من تمييز هذا الالتزام كمكتمل؟", "تأكيد", 
                        MessageBoxButton.YesNo, MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        selectedObligation.Status = "مكتمل";
                        selectedObligation.UpdatedDate = DateTime.Now;
                        
                        bool success = await _databaseService.UpdateObligationAsync(selectedObligation);
                        if (success)
                        {
                            await LoadObligations();
                            DXMessageBox.Show("تم تمييز الالتزام كمكتمل بنجاح", "نجح", 
                                MessageBoxButton.OK, MessageBoxImage.Information);
                        }
                        else
                        {
                            DXMessageBox.Show("فشل في تحديث حالة الالتزام", "خطأ", 
                                MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                }
                else
                {
                    DXMessageBox.Show("يرجى اختيار التزام", "تنبيه", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تمييز الالتزام كمكتمل: {ex.Message}");
                DXMessageBox.Show("خطأ في تمييز الالتزام كمكتمل", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void ReactivateObligation_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (ObligationsGrid.SelectedItem is Obligation selectedObligation)
                {
                    var result = DXMessageBox.Show("هل أنت متأكد من إعادة تفعيل هذا الالتزام؟", "تأكيد", 
                        MessageBoxButton.YesNo, MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        selectedObligation.Status = "نشط";
                        selectedObligation.UpdatedDate = DateTime.Now;
                        
                        bool success = await _databaseService.UpdateObligationAsync(selectedObligation);
                        if (success)
                        {
                            await LoadObligations();
                            DXMessageBox.Show("تم إعادة تفعيل الالتزام بنجاح", "نجح", 
                                MessageBoxButton.OK, MessageBoxImage.Information);
                        }
                        else
                        {
                            DXMessageBox.Show("فشل في تحديث حالة الالتزام", "خطأ", 
                                MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                }
                else
                {
                    DXMessageBox.Show("يرجى اختيار التزام", "تنبيه", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إعادة تفعيل الالتزام: {ex.Message}");
                DXMessageBox.Show("خطأ في إعادة تفعيل الالتزام", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void DeleteObligation_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (ObligationsGrid.SelectedItem is Obligation selectedObligation)
                {
                    var result = DXMessageBox.Show("هل أنت متأكد من حذف هذا الالتزام؟ لا يمكن التراجع عن هذا الإجراء.", "تأكيد الحذف", 
                        MessageBoxButton.YesNo, MessageBoxImage.Warning);

                    if (result == MessageBoxResult.Yes)
                    {
                        bool success = await _databaseService.DeleteObligationAsync(selectedObligation.Id);
                        if (success)
                        {
                            await LoadObligations();
                            DXMessageBox.Show("تم حذف الالتزام بنجاح", "نجح", 
                                MessageBoxButton.OK, MessageBoxImage.Information);
                        }
                        else
                        {
                            DXMessageBox.Show("فشل في حذف الالتزام", "خطأ", 
                                MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                }
                else
                {
                    DXMessageBox.Show("يرجى اختيار التزام للحذف", "تنبيه", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حذف الالتزام: {ex.Message}");
                DXMessageBox.Show("خطأ في حذف الالتزام", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
