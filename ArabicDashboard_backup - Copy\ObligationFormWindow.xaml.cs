using System;
using System.Windows;
using ArabicDashboard.Models;
using ArabicDashboard.Services;
using DevExpress.Xpf.Core;

namespace ArabicDashboard
{
    public partial class ObligationFormWindow : Window
    {
        private readonly DatabaseService _databaseService;
        private Obligation? _currentObligation;
        private readonly bool _isEditMode;
        private readonly bool _isViewMode;

        public event EventHandler? ObligationSaved;

        public ObligationFormWindow()
        {
            InitializeComponent();
            _databaseService = new DatabaseService();
            _isEditMode = false;
            _isViewMode = false;
            InitializeForm();
        }

        public ObligationFormWindow(Obligation obligation, bool viewMode = false)
        {
            InitializeComponent();
            _databaseService = new DatabaseService();
            _currentObligation = obligation;
            _isEditMode = true;
            _isViewMode = viewMode;
            InitializeForm();
            LoadObligationData();
        }

        private void InitializeForm()
        {
            if (_isViewMode)
            {
                TxtTitle.Text = "👁️ عرض تفاصيل الالتزام";
                BtnSave.Visibility = Visibility.Collapsed;
                BtnCancel.Content = "🔙 إغلاق";
                
                // تعطيل جميع الحقول
                TxtName.IsEnabled = false;
                CmbType.IsEnabled = false;
                CmbDuration.IsEnabled = false;
                DateDue.IsEnabled = false;
                NumAmount.IsEnabled = false;
                ChkReminderEnabled.IsEnabled = false;
                NumReminderDays.IsEnabled = false;
                TxtNotes.IsEnabled = false;
                
                InfoPanel.Visibility = Visibility.Visible;
            }
            else if (_isEditMode)
            {
                TxtTitle.Text = "✏️ تعديل الالتزام";
                InfoPanel.Visibility = Visibility.Visible;
            }
            else
            {
                TxtTitle.Text = "📋 إضافة التزام جديد";
                InfoPanel.Visibility = Visibility.Collapsed;
            }

            // تعيين التاريخ الافتراضي
            if (!_isEditMode)
            {
                DateDue.DateTime = DateTime.Now.AddDays(30);
            }
        }

        private void LoadObligationData()
        {
            if (_currentObligation == null) return;

            try
            {
                TxtName.Text = _currentObligation.Name;
                CmbType.Text = _currentObligation.Type;
                CmbDuration.Text = _currentObligation.Duration;
                DateDue.DateTime = _currentObligation.DueDate;
                NumAmount.Value = (decimal)_currentObligation.Amount;
                ChkReminderEnabled.IsChecked = _currentObligation.IsReminderEnabled;
                NumReminderDays.Value = (decimal)_currentObligation.ReminderDaysBefore;
                TxtNotes.Text = _currentObligation.Notes;

                // تحديث المعلومات الإضافية
                if (InfoPanel.Visibility == Visibility.Visible)
                {
                    TxtCreatedDate.Text = _currentObligation.CreatedDate.ToString("dd/MM/yyyy HH:mm");
                    TxtUpdatedDate.Text = _currentObligation.UpdatedDate?.ToString("dd/MM/yyyy HH:mm") ?? "لم يتم التحديث";
                    TxtCurrentStatus.Text = _currentObligation.StatusText;
                    TxtDaysRemaining.Text = _currentObligation.DaysRemaining.ToString();
                    
                    // تغيير لون الأيام المتبقية حسب الحالة
                    if (_currentObligation.IsOverdue)
                    {
                        TxtDaysRemaining.Foreground = System.Windows.Media.Brushes.Red;
                        TxtDaysRemaining.Text = "متأخر";
                    }
                    else if (_currentObligation.IsNearDue)
                    {
                        TxtDaysRemaining.Foreground = System.Windows.Media.Brushes.Orange;
                    }
                    else
                    {
                        TxtDaysRemaining.Foreground = System.Windows.Media.Brushes.Green;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل بيانات الالتزام: {ex.Message}");
                DXMessageBox.Show("خطأ في تحميل بيانات الالتزام", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!ValidateForm())
                    return;

                // إنشاء أو تحديث الالتزام
                if (_currentObligation == null)
                {
                    _currentObligation = new Obligation();
                }

                _currentObligation.Name = TxtName.Text.Trim();
                _currentObligation.Type = CmbType.Text.Trim();
                _currentObligation.Duration = CmbDuration.Text.Trim();
                _currentObligation.DueDate = DateDue.DateTime;
                _currentObligation.Amount = NumAmount.Value;
                _currentObligation.IsReminderEnabled = ChkReminderEnabled.IsChecked ?? true;
                _currentObligation.ReminderDaysBefore = (int)NumReminderDays.Value;
                _currentObligation.Notes = TxtNotes.Text.Trim();

                if (_isEditMode)
                {
                    _currentObligation.UpdatedDate = DateTime.Now;
                }
                else
                {
                    _currentObligation.CreatedDate = DateTime.Now;
                    _currentObligation.Status = "نشط";
                }

                // حفظ في قاعدة البيانات
                bool success;
                if (_isEditMode)
                {
                    success = await _databaseService.UpdateObligationAsync(_currentObligation);
                }
                else
                {
                    success = await _databaseService.AddObligationAsync(_currentObligation);
                }

                if (success)
                {
                    DXMessageBox.Show(
                        _isEditMode ? "تم تحديث الالتزام بنجاح" : "تم إضافة الالتزام بنجاح",
                        "نجح",
                        MessageBoxButton.OK,
                        MessageBoxImage.Information);

                    ObligationSaved?.Invoke(this, EventArgs.Empty);
                    Close();
                }
                else
                {
                    DXMessageBox.Show("فشل في حفظ الالتزام. يرجى المحاولة مرة أخرى.", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حفظ الالتزام: {ex.Message}");
                DXMessageBox.Show($"خطأ في حفظ الالتزام: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private bool ValidateForm()
        {
            // التحقق من الحقول المطلوبة
            if (string.IsNullOrWhiteSpace(TxtName.Text))
            {
                DXMessageBox.Show("يرجى إدخال اسم الالتزام", "حقل مطلوب", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtName.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(CmbType.Text))
            {
                DXMessageBox.Show("يرجى اختيار نوع الالتزام", "حقل مطلوب", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                CmbType.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(CmbDuration.Text))
            {
                DXMessageBox.Show("يرجى اختيار مدة الالتزام", "حقل مطلوب", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                CmbDuration.Focus();
                return false;
            }

            if (DateDue.DateTime == DateTime.MinValue)
            {
                DXMessageBox.Show("يرجى اختيار تاريخ الاستحقاق", "حقل مطلوب",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                DateDue.Focus();
                return false;
            }

            // التحقق من صحة التاريخ
            if (DateDue.DateTime < DateTime.Now.Date && !_isEditMode)
            {
                var result = DXMessageBox.Show("تاريخ الاستحقاق في الماضي. هل تريد المتابعة؟", "تأكيد", 
                    MessageBoxButton.YesNo, MessageBoxImage.Question);
                if (result == MessageBoxResult.No)
                {
                    DateDue.Focus();
                    return false;
                }
            }

            // التحقق من عدد أيام التنبيه
            if (ChkReminderEnabled.IsChecked == true)
            {
                if (NumReminderDays.Value <= 0)
                {
                    DXMessageBox.Show("يرجى إدخال عدد أيام صحيح للتنبيه", "قيمة غير صحيحة",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    NumReminderDays.Focus();
                    return false;
                }
            }

            return true;
        }

        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            if (!_isViewMode && HasUnsavedChanges())
            {
                var result = DXMessageBox.Show("هناك تغييرات غير محفوظة. هل تريد الخروج بدون حفظ؟", "تأكيد", 
                    MessageBoxButton.YesNo, MessageBoxImage.Question);
                if (result == MessageBoxResult.No)
                    return;
            }

            Close();
        }

        private bool HasUnsavedChanges()
        {
            if (_currentObligation == null)
            {
                return !string.IsNullOrWhiteSpace(TxtName.Text) ||
                       !string.IsNullOrWhiteSpace(CmbType.Text) ||
                       !string.IsNullOrWhiteSpace(CmbDuration.Text) ||
                       !string.IsNullOrWhiteSpace(TxtNotes.Text);
            }

            return TxtName.Text.Trim() != _currentObligation.Name ||
                   CmbType.Text.Trim() != _currentObligation.Type ||
                   CmbDuration.Text.Trim() != _currentObligation.Duration ||
                   DateDue.DateTime != _currentObligation.DueDate ||
                   NumAmount.Value != _currentObligation.Amount ||
                   (ChkReminderEnabled.IsChecked ?? true) != _currentObligation.IsReminderEnabled ||
                   (int)NumReminderDays.Value != _currentObligation.ReminderDaysBefore ||
                   TxtNotes.Text.Trim() != _currentObligation.Notes;
        }

        protected override void OnClosing(System.ComponentModel.CancelEventArgs e)
        {
            if (!_isViewMode && HasUnsavedChanges())
            {
                var result = DXMessageBox.Show("هناك تغييرات غير محفوظة. هل تريد الخروج بدون حفظ؟", "تأكيد", 
                    MessageBoxButton.YesNo, MessageBoxImage.Question);
                if (result == MessageBoxResult.No)
                {
                    e.Cancel = true;
                    return;
                }
            }

            base.OnClosing(e);
        }

        private async void BtnSaveAndNew_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!ValidateForm())
                    return;

                // حفظ الالتزام الحالي
                await SaveObligation();

                // مسح النموذج لإضافة التزام جديد
                ClearForm();
                TxtName.Focus();

                // تغيير العنوان
                TxtTitle.Text = "📋 إضافة التزام جديد";

                DXMessageBox.Show("تم حفظ الالتزام بنجاح! ✅\nيمكنك الآن إضافة التزام جديد.", "نجح الحفظ",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حفظ الالتزام: {ex.Message}");
                DXMessageBox.Show($"خطأ في حفظ الالتزام: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ClearForm()
        {
            TxtName.Text = "";
            CmbType.EditValue = null;
            CmbDuration.EditValue = null;
            DateDue.EditValue = null;
            NumAmount.Value = 0;
            TxtNotes.Text = "";
            ChkReminderEnabled.IsChecked = true;
            NumReminderDays.Value = 7;

            // إعادة تعيين الوضع
            _isEditMode = false;
            _currentObligation = null;
        }
    }
}
