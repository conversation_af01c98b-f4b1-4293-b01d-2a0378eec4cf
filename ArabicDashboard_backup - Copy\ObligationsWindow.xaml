<Window x:Class="ArabicDashboard.ObligationsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core"
        xmlns:dxg="http://schemas.devexpress.com/winfx/2008/xaml/grid"
        xmlns:dxe="http://schemas.devexpress.com/winfx/2008/xaml/editors"
        Title="📋 إدارة الالتزامات - مكتب الخدمات العامة"
        Height="800" Width="1400"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized"
        Background="#F8FAFC"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        dx:ThemeManager.ThemeName="Office2019Colorful">

    <Window.Resources>
        <!-- أنماط الأزرار -->
        <Style x:Key="ModernButton" TargetType="Button">
            <Setter Property="Background" Value="#3B82F6"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="16,10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border"
                                Background="{TemplateBinding Background}"
                                CornerRadius="8"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#2563EB"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#1D4ED8"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SecondaryButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
            <Setter Property="Background" Value="#6B7280"/>
        </Style>

        <Style x:Key="DangerButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
            <Setter Property="Background" Value="#EF4444"/>
        </Style>

        <Style x:Key="SuccessButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
            <Setter Property="Background" Value="#10B981"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- الشريط العلوي -->
        <Border Grid.Row="0" Background="White" BorderBrush="#E2E8F0" BorderThickness="0,0,0,1" Padding="24,16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- العنوان والوصف -->
                <StackPanel Grid.Column="0" VerticalAlignment="Center">
                    <TextBlock Text="📋 إدارة الالتزامات" FontSize="24" FontWeight="Bold" Foreground="#1F2937" Margin="0,0,0,4"/>
                    <TextBlock Text="إدارة وتتبع جميع الالتزامات والمواعيد المهمة" FontSize="14" Foreground="#6B7280"/>
                </StackPanel>

                <!-- أزرار الإجراءات -->
                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                    <Button x:Name="BtnAddObligation" Content="➕ إضافة التزام جديد" Style="{StaticResource ModernButton}"
                            Margin="0,0,8,0" Click="BtnAddObligation_Click"/>
                    <Button x:Name="BtnRefresh" Content="🔄 تحديث" Style="{StaticResource SecondaryButton}"
                            Margin="0,0,8,0" Click="BtnRefresh_Click"/>
                    <Button x:Name="BtnImport" Content="📥 استيراد" Style="{StaticResource ModernButton}"
                            Background="#8B5CF6" Margin="0,0,8,0" Click="BtnImport_Click"/>
                    <Button x:Name="BtnExport" Content="📤 تصدير" Style="{StaticResource SuccessButton}"
                            Margin="0,0,8,0" Click="BtnExport_Click"/>
                    <Button x:Name="BtnPrint" Content="🖨️ طباعة" Style="{StaticResource ModernButton}"
                            Background="#F59E0B" Click="BtnPrint_Click"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- المحتوى الرئيسي -->
        <Border Grid.Row="1" Background="White" Margin="24" CornerRadius="12" 
                BorderBrush="#E2E8F0" BorderThickness="1">
            <Border.Effect>
                <DropShadowEffect Color="Black" Direction="270" ShadowDepth="4" Opacity="0.1" BlurRadius="20"/>
            </Border.Effect>

            <Grid Margin="24">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- شريط البحث والفلترة -->
                <Border Grid.Row="0" Background="#F8FAFC" CornerRadius="8" Padding="16" Margin="0,0,0,16">
                    <StackPanel>
                        <!-- الصف الأول: البحث والفلاتر الأساسية -->
                        <Grid Margin="0,0,0,12">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBox x:Name="TxtSearch" Grid.Column="0"
                                     FontSize="14" Padding="12,8"
                                     Background="White" BorderBrush="#D1D5DB" BorderThickness="1"
                                     Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                                     VerticalAlignment="Center" Margin="0,0,12,0">
                                <TextBox.Style>
                                    <Style TargetType="TextBox">
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate TargetType="TextBox">
                                                    <Border Background="{TemplateBinding Background}"
                                                            BorderBrush="{TemplateBinding BorderBrush}"
                                                            BorderThickness="{TemplateBinding BorderThickness}"
                                                            CornerRadius="6">
                                                        <Grid>
                                                            <TextBlock Text="🔍 البحث في الالتزامات..."
                                                                       Foreground="#9CA3AF"
                                                                       Margin="12,0,0,0"
                                                                       VerticalAlignment="Center"
                                                                       IsHitTestVisible="False">
                                                                <TextBlock.Style>
                                                                    <Style TargetType="TextBlock">
                                                                        <Setter Property="Visibility" Value="Collapsed"/>
                                                                        <Style.Triggers>
                                                                            <DataTrigger Binding="{Binding Text, RelativeSource={RelativeSource TemplatedParent}}" Value="">
                                                                                <Setter Property="Visibility" Value="Visible"/>
                                                                            </DataTrigger>
                                                                        </Style.Triggers>
                                                                    </Style>
                                                                </TextBlock.Style>
                                                            </TextBlock>
                                                            <ScrollViewer x:Name="PART_ContentHost" Margin="12,0,0,0" VerticalAlignment="Center"/>
                                                        </Grid>
                                                    </Border>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Style>
                                </TextBox.Style>
                            </TextBox>

                            <ComboBox x:Name="CmbStatusFilter" Grid.Column="1"
                                      Width="150" FontSize="14" Padding="12,8"
                                      Background="White" BorderBrush="#D1D5DB" BorderThickness="1"
                                      SelectedValuePath="Content" VerticalAlignment="Center" Margin="0,0,12,0"
                                      SelectionChanged="CmbStatusFilter_SelectionChanged">
                                <ComboBoxItem Content="جميع الحالات" IsSelected="True"/>
                                <ComboBoxItem Content="نشط"/>
                                <ComboBoxItem Content="مكتمل"/>
                                <ComboBoxItem Content="متأخر"/>
                                <ComboBoxItem Content="قريب الاستحقاق"/>
                            </ComboBox>

                            <ComboBox x:Name="CmbTypeFilter" Grid.Column="2"
                                      Width="150" FontSize="14" Padding="12,8"
                                      Background="White" BorderBrush="#D1D5DB" BorderThickness="1"
                                      SelectedValuePath="Content" VerticalAlignment="Center" Margin="0,0,12,0"
                                      SelectionChanged="CmbTypeFilter_SelectionChanged">
                                <ComboBoxItem Content="جميع الأنواع" IsSelected="True"/>
                                <ComboBoxItem Content="التزام مالي"/>
                                <ComboBoxItem Content="التزام قانوني"/>
                                <ComboBoxItem Content="التزام إداري"/>
                                <ComboBoxItem Content="التزام تجاري"/>
                                <ComboBoxItem Content="التزام حكومي"/>
                                <ComboBoxItem Content="التزام تأميني"/>
                            </ComboBox>

                            <Button x:Name="BtnSearch" Grid.Column="3" Content="🔍 بحث"
                                    Style="{StaticResource ModernButton}" Click="BtnSearch_Click"/>
                        </Grid>

                        <!-- الصف الثاني: فلاتر التاريخ والمبلغ -->
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0" Text="من تاريخ:" VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <DatePicker x:Name="DateFrom" Grid.Column="1" FontSize="12" Margin="0,0,16,0"
                                       SelectedDateChanged="DateFilter_Changed"/>

                            <TextBlock Grid.Column="2" Text="إلى تاريخ:" VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <DatePicker x:Name="DateTo" Grid.Column="3" FontSize="12" Margin="0,0,16,0"
                                       SelectedDateChanged="DateFilter_Changed"/>

                            <Button x:Name="BtnClearFilters" Grid.Column="4" Content="🗑️ مسح الفلاتر"
                                    Style="{StaticResource SecondaryButton}" FontSize="12" Padding="8,4"
                                    Margin="0,0,8,0" Click="BtnClearFilters_Click"/>

                            <Button x:Name="BtnAdvancedSearch" Grid.Column="5" Content="🔧 بحث متقدم"
                                    Style="{StaticResource ModernButton}" FontSize="12" Padding="8,4"
                                    Background="#8B5CF6" Click="BtnAdvancedSearch_Click"/>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- جدول الالتزامات -->
                <dxg:GridControl x:Name="ObligationsGrid" Grid.Row="1" 
                                 AutoGenerateColumns="False"
                                 EnableSmartColumnsGeneration="True"
                                 SelectionMode="Row">
                    
                    <dxg:GridControl.Columns>
                        <dxg:GridColumn FieldName="Name" Header="اسم الالتزام" Width="200"/>
                        <dxg:GridColumn FieldName="Type" Header="نوع الالتزام" Width="150"/>
                        <dxg:GridColumn FieldName="Duration" Header="مدة الالتزام" Width="120"/>
                        <dxg:GridColumn FieldName="DueDate" Header="تاريخ الاستحقاق" Width="150">
                            <dxg:GridColumn.EditSettings>
                                <dxe:DateEditSettings DisplayFormat="dd/MM/yyyy"/>
                            </dxg:GridColumn.EditSettings>
                        </dxg:GridColumn>
                        <dxg:GridColumn FieldName="DaysRemaining" Header="الأيام المتبقية" Width="120"/>
                        <dxg:GridColumn FieldName="Amount" Header="المبلغ" Width="120">
                            <dxg:GridColumn.EditSettings>
                                <dxe:SpinEditSettings DisplayFormat="N2"/>
                            </dxg:GridColumn.EditSettings>
                        </dxg:GridColumn>
                        <dxg:GridColumn FieldName="StatusText" Header="الحالة" Width="120"/>
                        <dxg:GridColumn FieldName="CreatedDate" Header="تاريخ الإنشاء" Width="150">
                            <dxg:GridColumn.EditSettings>
                                <dxe:DateEditSettings DisplayFormat="dd/MM/yyyy"/>
                            </dxg:GridColumn.EditSettings>
                        </dxg:GridColumn>
                    </dxg:GridControl.Columns>

                    <dxg:GridControl.View>
                        <dxg:TableView ShowGroupPanel="False" 
                                       AutoWidth="True"
                                       AllowEditing="False"
                                       ShowIndicator="True"
                                       ShowVerticalLines="False"
                                       ShowHorizontalLines="True"
                                       RowDoubleClick="TableView_RowDoubleClick"/>
                    </dxg:GridControl.View>

                    <dxg:GridControl.ContextMenu>
                        <ContextMenu>
                            <MenuItem Header="✏️ تعديل" Click="EditObligation_Click"/>
                            <MenuItem Header="👁️ عرض التفاصيل" Click="ViewObligation_Click"/>
                            <Separator/>
                            <MenuItem Header="✅ تمييز كمكتمل" Click="MarkAsCompleted_Click"/>
                            <MenuItem Header="🔄 إعادة تفعيل" Click="ReactivateObligation_Click"/>
                            <Separator/>
                            <MenuItem Header="🗑️ حذف" Click="DeleteObligation_Click"/>
                        </ContextMenu>
                    </dxg:GridControl.ContextMenu>
                </dxg:GridControl>
            </Grid>
        </Border>

        <!-- شريط الحالة -->
        <Border Grid.Row="2" Background="#F8FAFC" BorderBrush="#E2E8F0" BorderThickness="0,1,0,0" Padding="24,12">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBlock Text="📊 إجمالي الالتزامات: " FontSize="14" FontWeight="Medium" Foreground="#374151"/>
                    <TextBlock x:Name="TxtTotalObligations" Text="0" FontSize="14" FontWeight="Bold" Foreground="#3B82F6" Margin="4,0,16,0"/>
                    <TextBlock Text="| نشط: " FontSize="14" FontWeight="Medium" Foreground="#374151"/>
                    <TextBlock x:Name="TxtActiveObligations" Text="0" FontSize="14" FontWeight="Bold" Foreground="#10B981" Margin="4,0,16,0"/>
                    <TextBlock Text="| متأخر: " FontSize="14" FontWeight="Medium" Foreground="#374151"/>
                    <TextBlock x:Name="TxtOverdueObligations" Text="0" FontSize="14" FontWeight="Bold" Foreground="#EF4444" Margin="4,0"/>
                </StackPanel>

                <TextBlock Grid.Column="1" x:Name="TxtLastUpdate" Text="آخر تحديث: --" 
                           FontSize="12" Foreground="#6B7280" VerticalAlignment="Center"/>
            </Grid>
        </Border>
    </Grid>
</Window>
