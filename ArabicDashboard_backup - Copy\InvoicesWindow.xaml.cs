using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using ArabicDashboard.Models;
using ArabicDashboard.Services;
using DevExpress.Xpf.Core;

namespace ArabicDashboard
{
    public partial class InvoicesWindow : Window
    {
        private readonly DatabaseService _databaseService;
        private List<Invoice> _invoices;

        public InvoicesWindow()
        {
            InitializeComponent();
            _databaseService = new DatabaseService();
            _invoices = new List<Invoice>();

            Loaded += InvoicesWindow_Loaded;
        }

        private async void InvoicesWindow_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadInvoices();
            UpdateLastUpdateTime();

            // ربط أحداث الجدول
            GridInvoices.SelectionChanged += GridInvoices_SelectionChanged;
        }

        private void GridInvoices_SelectionChanged(object sender, DevExpress.Xpf.Grid.GridSelectionChangedEventArgs e)
        {
            UpdateSelectedCount();
        }

        private void UpdateSelectedCount()
        {
            var selectedCount = GridInvoices.SelectedItems.Count;
            TxtSelectedCount.Text = selectedCount.ToString();
        }

        private void UpdateLastUpdateTime()
        {
            TxtLastUpdate.Text = $"آخر تحديث: {DateTime.Now:HH:mm:ss}";
        }

        private async Task LoadInvoices()
        {
            try
            {
                // الفواتير مرتبة بالفعل من قاعدة البيانات (الأحدث أولاً)
                _invoices = await _databaseService.GetAllInvoicesAsync();
                GridInvoices.ItemsSource = _invoices;
                UpdateStatistics();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل الفواتير: {ex.Message}");
                DXMessageBox.Show($"خطأ في تحميل الفواتير: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateStatistics()
        {
            try
            {
                TxtTotalInvoices.Text = _invoices.Count.ToString();
                TxtPaidInvoices.Text = _invoices.Count(i => i.Status == "مُسدد").ToString();
                TxtPendingInvoices.Text = _invoices.Count(i => i.Status != "مُسدد").ToString();
                TxtTotalAmount.Text = $"{_invoices.Sum(i => i.Amount):N2} ريال";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث الإحصائيات: {ex.Message}");
            }
        }

        private void BtnAddInvoice_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var addWindow = new InvoiceFormWindow();
                addWindow.InvoiceSaved += async (s, args) => await LoadInvoices();
                addWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في فتح نافذة إضافة الفاتورة: {ex.Message}");
                DXMessageBox.Show("خطأ في فتح نافذة إضافة الفاتورة", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void BtnRefresh_Click(object sender, RoutedEventArgs e)
        {
            await LoadInvoices();
            UpdateLastUpdateTime();
            DXMessageBox.Show("تم تحديث البيانات بنجاح! ✅", "تحديث البيانات",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void BtnEdit_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (GridInvoices.SelectedItem is Invoice selectedInvoice)
                {
                    var editWindow = new InvoiceFormWindow(selectedInvoice);
                    editWindow.InvoiceSaved += async (s, args) => await LoadInvoices();
                    editWindow.ShowDialog();
                }
                else
                {
                    DXMessageBox.Show("يرجى اختيار فاتورة للتعديل", "تنبيه", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تعديل الفاتورة: {ex.Message}");
                DXMessageBox.Show("خطأ في تعديل الفاتورة", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void BtnDelete_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (GridInvoices.SelectedItem is Invoice selectedInvoice)
                {
                    var result = DXMessageBox.Show(
                        $"هل أنت متأكد من حذف الفاتورة رقم {selectedInvoice.InvoiceNumber}؟",
                        "تأكيد الحذف",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        bool deleted = await _databaseService.DeleteInvoiceAsync(selectedInvoice.Id);
                        if (deleted)
                        {
                            await LoadInvoices();
                            DXMessageBox.Show("تم حذف الفاتورة بنجاح", "نجح", 
                                MessageBoxButton.OK, MessageBoxImage.Information);
                        }
                        else
                        {
                            DXMessageBox.Show("فشل في حذف الفاتورة", "خطأ", 
                                MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                }
                else
                {
                    DXMessageBox.Show("يرجى اختيار فاتورة للحذف", "تنبيه", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حذف الفاتورة: {ex.Message}");
                DXMessageBox.Show("خطأ في حذف الفاتورة", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnPrint_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (GridInvoices.SelectedItem is Invoice selectedInvoice)
                {
                    var previewWindow = new InvoicePreviewWindow(selectedInvoice);
                    previewWindow.ShowDialog();
                }
                else
                {
                    DXMessageBox.Show("يرجى اختيار فاتورة للطباعة", "تنبيه", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في طباعة الفاتورة: {ex.Message}");
                DXMessageBox.Show("خطأ في طباعة الفاتورة", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnExport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (GridInvoices.SelectedItem is Invoice selectedInvoice)
                {
                    // فتح نافذة المعاينة مع إمكانية التصدير
                    var previewWindow = new InvoicePreviewWindow(selectedInvoice);
                    previewWindow.ShowDialog();
                }
                else
                {
                    DXMessageBox.Show("يرجى اختيار فاتورة للتصدير", "تنبيه",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تصدير الفاتورة: {ex.Message}");
                DXMessageBox.Show("خطأ في تصدير الفاتورة", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // الوظائف الجديدة المضافة
        private void BtnSearch_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                DXMessageBox.Show("ميزة البحث المتقدم قيد التطوير وستكون متاحة قريباً! 🔍", "قريباً",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في البحث المتقدم: {ex.Message}");
            }
        }

        private void BtnBulkActions_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var selectedItems = GridInvoices.SelectedItems.Cast<Invoice>().ToList();
                if (selectedItems.Count == 0)
                {
                    DXMessageBox.Show("يرجى اختيار فاتورة أو أكثر لتطبيق الإجراءات المجمعة", "تنبيه",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                DXMessageBox.Show($"تم اختيار {selectedItems.Count} فاتورة.\nميزة الإجراءات المجمعة قيد التطوير! ⚡", "قريباً",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في الإجراءات المجمعة: {ex.Message}");
            }
        }

        private void BtnReports_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                DXMessageBox.Show("ميزة التقارير المتقدمة قيد التطوير وستشمل:\n\n📊 تقارير مالية شاملة\n📈 إحصائيات تفصيلية\n📋 تقارير مخصصة\n📄 تصدير متعدد الصيغ", "قريباً",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في التقارير: {ex.Message}");
            }
        }

        // وظائف القائمة السياقية
        private void EditInvoice_Click(object sender, RoutedEventArgs e)
        {
            BtnEdit_Click(sender, e);
        }

        private void ViewInvoice_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (GridInvoices.SelectedItem is Invoice selectedInvoice)
                {
                    // عرض تفاصيل الفاتورة (يمكن تطوير نافذة عرض مخصصة لاحقاً)
                    var details = $"تفاصيل الفاتورة:\n\n" +
                                 $"رقم الفاتورة: {selectedInvoice.InvoiceNumber}\n" +
                                 $"العميل: {selectedInvoice.ClientName}\n" +
                                 $"الهاتف: {selectedInvoice.PhoneNumber}\n" +
                                 $"نوع المعاملة: {selectedInvoice.TransactionType}\n" +
                                 $"المبلغ: {selectedInvoice.Amount:N0} ريال\n" +
                                 $"المبلغ المدفوع: {selectedInvoice.PaidAmount:N0} ريال\n" +
                                 $"الحالة: {selectedInvoice.Status}\n" +
                                 $"تاريخ الإنشاء: {selectedInvoice.CreatedDate:dd/MM/yyyy}";

                    DXMessageBox.Show(details, "تفاصيل الفاتورة",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    DXMessageBox.Show("يرجى اختيار فاتورة للعرض", "تنبيه",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في عرض الفاتورة: {ex.Message}");
            }
        }

        private void PrintInvoice_Click(object sender, RoutedEventArgs e)
        {
            BtnPrint_Click(sender, e);
        }

        private void ExportInvoice_Click(object sender, RoutedEventArgs e)
        {
            BtnExport_Click(sender, e);
        }

        private void PayInvoice_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (GridInvoices.SelectedItem is Invoice selectedInvoice)
                {
                    if (selectedInvoice.Status == "مسددة")
                    {
                        DXMessageBox.Show("هذه الفاتورة مسددة بالفعل", "تنبيه",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                        return;
                    }

                    var result = DXMessageBox.Show($"هل تريد تسديد الفاتورة رقم {selectedInvoice.InvoiceNumber}؟\nالمبلغ: {selectedInvoice.Amount:N0} ريال",
                        "تأكيد التسديد", MessageBoxButton.YesNo, MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        // تحديث حالة الفاتورة
                        selectedInvoice.Status = "مسددة";
                        selectedInvoice.PaidAmount = selectedInvoice.Amount;

                        // حفظ في قاعدة البيانات (يمكن إضافة هذه الوظيفة لاحقاً)
                        DXMessageBox.Show("تم تسديد الفاتورة بنجاح! ✅", "نجح التسديد",
                            MessageBoxButton.OK, MessageBoxImage.Information);

                        // تحديث العرض
                        LoadInvoices();
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تسديد الفاتورة: {ex.Message}");
            }
        }

        private void DeleteInvoice_Click(object sender, RoutedEventArgs e)
        {
            BtnDelete_Click(sender, e);
        }
    }
}
