﻿using System.Configuration;
using System.Data;
using System.Windows;
using System.Windows.Media;
using DevExpress.Xpf.Core;
using ArabicDashboard.Services;

namespace ArabicDashboard;

/// <summary>
/// Interaction logic for App.xaml
/// </summary>
public partial class App : System.Windows.Application
{
    private SettingsService? _settingsService;
    private ThemeService? _themeService;

    protected override void OnStartup(StartupEventArgs e)
    {
        try
        {
            // إضافة معالج للأخطاء غير المعالجة
            this.DispatcherUnhandledException += App_DispatcherUnhandledException;
            AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;

            // تهيئة الخدمات
            _settingsService = new SettingsService();
            _themeService = new ThemeService();

            // تطبيق الثيم المحفوظ
            _themeService.ApplySavedTheme();

            // تطبيق إعدادات الخط (معطل مؤقتاً)
            // ApplyFontSettings();

            base.OnStartup(e);
        }
        catch (System.Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في بدء التطبيق: {ex.Message}");
            System.Diagnostics.Debug.WriteLine($"Stack Trace: {ex.StackTrace}");
            System.Windows.MessageBox.Show($"خطأ في بدء التطبيق:\n{ex.Message}\n\nStack Trace:\n{ex.StackTrace}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            base.OnStartup(e);
        }
    }

    private void App_DispatcherUnhandledException(object sender, System.Windows.Threading.DispatcherUnhandledExceptionEventArgs e)
    {
        try
        {
            System.Diagnostics.Debug.WriteLine($"خطأ غير معالج في Dispatcher: {e.Exception.Message}");
            System.Diagnostics.Debug.WriteLine($"Stack Trace: {e.Exception.StackTrace}");
            System.Windows.MessageBox.Show($"خطأ غير معالج:\n{e.Exception.Message}\n\nStack Trace:\n{e.Exception.StackTrace}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            e.Handled = true;
        }
        catch
        {
            // تجنب حلقة لا نهائية من الأخطاء
        }
    }

    private void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
    {
        try
        {
            var ex = e.ExceptionObject as System.Exception;
            if (ex != null)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ غير معالج في AppDomain: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack Trace: {ex.StackTrace}");
                System.Windows.MessageBox.Show($"خطأ غير معالج:\n{ex.Message}\n\nStack Trace:\n{ex.StackTrace}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        catch
        {
            // تجنب حلقة لا نهائية من الأخطاء
        }
    }

    private void ApplyTheme()
    {
        try
        {
            var themeName = _settingsService?.GetSetting("Theme", "Office2019Colorful") ?? "Office2019Colorful";

            // تطبيق ثيم DevExpress
            switch (themeName)
            {
                case "Office2019Colorful":
                    ApplicationThemeHelper.ApplicationThemeName = Theme.Office2019ColorfulName;
                    break;
                case "Office2019White":
                    ApplicationThemeHelper.ApplicationThemeName = Theme.Office2019WhiteName;
                    break;
                case "Office2019Black":
                    ApplicationThemeHelper.ApplicationThemeName = Theme.Office2019BlackName;
                    break;
                case "Office2019HighContrast":
                    ApplicationThemeHelper.ApplicationThemeName = Theme.Office2019HighContrastName;
                    break;
                case "VS2019Blue":
                    ApplicationThemeHelper.ApplicationThemeName = Theme.VS2019BlueName;
                    break;
                case "VS2019Dark":
                    ApplicationThemeHelper.ApplicationThemeName = Theme.VS2019DarkName;
                    break;
                case "VS2019Light":
                    ApplicationThemeHelper.ApplicationThemeName = Theme.VS2019LightName;
                    break;
                case "Win10Light":
                    ApplicationThemeHelper.ApplicationThemeName = Theme.Win10LightName;
                    break;
                case "Win10Dark":
                    ApplicationThemeHelper.ApplicationThemeName = Theme.Win10DarkName;
                    break;
                case "ComfortableTheme":
                    ApplicationThemeHelper.ApplicationThemeName = Theme.Office2019ColorfulName;
                    ApplyComfortableTheme();
                    break;
                default:
                    ApplicationThemeHelper.ApplicationThemeName = Theme.Office2019ColorfulName;
                    break;
            }

            // تطبيق ألوان مخصصة على الواجهة
            ApplyCustomColors(themeName);
        }
        catch (System.Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق الثيم: {ex.Message}");
            // استخدام الثيم الافتراضي في حالة الخطأ
            ApplicationThemeHelper.ApplicationThemeName = Theme.Office2019ColorfulName;
        }
    }

    private void ApplyCustomColors(string themeName)
    {
        try
        {
            var resources = this.Resources;

            switch (themeName)
            {
                case "Office2019Colorful":
                case "ComfortableTheme":
                    resources["PrimaryBrush"] = new SolidColorBrush(System.Windows.Media.Color.FromRgb(37, 99, 235)); // أزرق مريح
                    resources["SecondaryBrush"] = new SolidColorBrush(System.Windows.Media.Color.FromRgb(100, 116, 139));
                    resources["BackgroundBrush"] = new SolidColorBrush(System.Windows.Media.Color.FromRgb(248, 250, 252));
                    resources["CardBrush"] = new SolidColorBrush(System.Windows.Media.Colors.White);
                    resources["TextBrush"] = new SolidColorBrush(System.Windows.Media.Color.FromRgb(30, 41, 59));
                    break;

                case "Office2019White":
                    resources["PrimaryBrush"] = new SolidColorBrush(System.Windows.Media.Color.FromRgb(0, 120, 215));
                    resources["SecondaryBrush"] = new SolidColorBrush(System.Windows.Media.Color.FromRgb(96, 94, 92));
                    resources["BackgroundBrush"] = new SolidColorBrush(System.Windows.Media.Colors.White);
                    resources["CardBrush"] = new SolidColorBrush(System.Windows.Media.Color.FromRgb(250, 250, 250));
                    resources["TextBrush"] = new SolidColorBrush(System.Windows.Media.Color.FromRgb(50, 49, 48));
                    break;

                case "Office2019Black":
                case "VS2019Dark":
                    resources["PrimaryBrush"] = new SolidColorBrush(System.Windows.Media.Color.FromRgb(0, 120, 215));
                    resources["SecondaryBrush"] = new SolidColorBrush(System.Windows.Media.Color.FromRgb(160, 160, 160));
                    resources["BackgroundBrush"] = new SolidColorBrush(System.Windows.Media.Color.FromRgb(45, 45, 48));
                    resources["CardBrush"] = new SolidColorBrush(System.Windows.Media.Color.FromRgb(60, 60, 65));
                    resources["TextBrush"] = new SolidColorBrush(System.Windows.Media.Colors.White);
                    break;

                case "VS2019Blue":
                    resources["PrimaryBrush"] = new SolidColorBrush(System.Windows.Media.Color.FromRgb(0, 122, 204));
                    resources["SecondaryBrush"] = new SolidColorBrush(System.Windows.Media.Color.FromRgb(104, 104, 104));
                    resources["BackgroundBrush"] = new SolidColorBrush(System.Windows.Media.Color.FromRgb(246, 246, 246));
                    resources["CardBrush"] = new SolidColorBrush(System.Windows.Media.Colors.White);
                    resources["TextBrush"] = new SolidColorBrush(System.Windows.Media.Color.FromRgb(30, 30, 30));
                    break;
            }
        }
        catch (System.Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق الألوان المخصصة: {ex.Message}");
        }
    }

    private void ApplyComfortableTheme()
    {
        try
        {
            var resources = this.Resources;

            // ثيم مريح للعين مع ألوان متناسقة
            resources["PrimaryBrush"] = new SolidColorBrush(System.Windows.Media.Color.FromRgb(52, 152, 219)); // أزرق فاتح مريح
            resources["SecondaryBrush"] = new SolidColorBrush(System.Windows.Media.Color.FromRgb(149, 165, 166)); // رمادي فاتح
            resources["AccentBrush"] = new SolidColorBrush(System.Windows.Media.Color.FromRgb(46, 204, 113)); // أخضر مريح
            resources["WarningBrush"] = new SolidColorBrush(System.Windows.Media.Color.FromRgb(241, 196, 15)); // أصفر ذهبي
            resources["DangerBrush"] = new SolidColorBrush(System.Windows.Media.Color.FromRgb(231, 76, 60)); // أحمر مريح
            resources["BackgroundBrush"] = new SolidColorBrush(System.Windows.Media.Color.FromRgb(250, 252, 255)); // خلفية بيضاء مزرقة
            resources["CardBrush"] = new SolidColorBrush(System.Windows.Media.Colors.White);
            resources["TextBrush"] = new SolidColorBrush(System.Windows.Media.Color.FromRgb(44, 62, 80)); // نص داكن مريح
            resources["MutedBrush"] = new SolidColorBrush(System.Windows.Media.Color.FromRgb(127, 140, 141));
        }
        catch (System.Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق الثيم المريح: {ex.Message}");
        }
    }

    private void ApplyFontSettings()
    {
        try
        {
            var fontFamily = _settingsService?.GetSetting("FontFamily", "Segoe UI") ?? "Segoe UI";
            var fontSize = _settingsService?.GetSetting("FontSize", 14) ?? 14;

            // تطبيق الخط على مستوى التطبيق
            this.Resources["DefaultFontFamily"] = new System.Windows.Media.FontFamily(fontFamily);
            this.Resources["DefaultFontSize"] = (double)fontSize;
        }
        catch (System.Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق إعدادات الخط: {ex.Message}");
        }
    }

    public static void RefreshTheme()
    {
        try
        {
            var app = Current as App;
            if (app != null)
            {
                // إعادة تهيئة خدمة الإعدادات إذا لم تكن موجودة
                if (app._settingsService == null)
                {
                    app._settingsService = new SettingsService();
                }

                app.ApplyTheme();
                app.ApplyFontSettings();
            }
        }
        catch (System.Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في تحديث الثيم: {ex.Message}");
        }
    }
}

